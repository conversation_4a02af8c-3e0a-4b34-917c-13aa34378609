import requests
from bs4 import BeautifulSoup
import pandas as pd
import numpy as np
from scipy import stats
import itertools
import time
import json

headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

def fetch_lottery_data(page=1, page_size=30):
    url = "https://webapi.sporttery.cn/gateway/lottery/getHistoryPageListV1.qry"
    params = {
        "gameNo": "85",
        "provinceId": "0",
        "pageSize": str(page_size),
        "isVerify": "1",
        "pageNo": str(page),
    }
    
    try:
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()
        data = response.json()
        
        if data['success']:
            return data['value']['list']
        else:
            print(f"API返回错误: {data['message']}")
            return []
    except Exception as e:
        print(f"请求失败: {e}")
        return []

def parse_lottery_data(lottery_list):
    data = []
    for item in lottery_list:
        draw_number = item['lotteryDrawNum']
        draw_date = item['lotteryDrawTime']
        numbers = item['lotteryDrawResult'].split()
        red_balls = [int(num) for num in numbers[:5]]
        blue_balls = [int(num) for num in numbers[5:]]
        data.append([draw_number, draw_date] + red_balls + blue_balls)
    return data

def calculate_consecutive_numbers(x):
    sorted_nums = sorted(x)
    consecutive_count = 0
    for i in range(1, len(sorted_nums)):
        if sorted_nums[i] == sorted_nums[i-1] + 1:
            consecutive_count += 1
    return consecutive_count

def calculate_analysis(df):
    # 前区和值
    df['前区和值'] = df[['前区1', '前区2', '前区3', '前区4', '前区5']].sum(axis=1)
    
    # 前区MA5和EMA5（反向计算）
    df['前区和值MA5'] = df['前区和值'][::-1].rolling(window=5).mean()[::-1]
    df['前区和值EMA5'] = df['前区和值'][::-1].ewm(span=5, adjust=False).mean()[::-1]
    
    # 前区奇偶比（转换为比值）
    def odd_even_ratio(row):
        odd_count = sum(1 for x in row if x % 2 != 0)
        even_count = sum(1 for x in row if x % 2 == 0)
        return f"{odd_count}:{even_count}"
    
    df['前区奇偶比'] = df[['前区1', '前区2', '前区3', '前区4', '前区5']].apply(odd_even_ratio, axis=1)
    
    # 前区方差、标准差、偏度、峰度
    df['前区方差'] = df[['前区1', '前区2', '前区3', '前区4', '前区5']].var(axis=1)
    df['前区标准差'] = df[['前区1', '前区2', '前区3', '前区4', '前区5']].std(axis=1)
    df['前区偏度'] = df[['前区1', '前区2', '前区3', '前区4', '前区5']].apply(lambda x: stats.skew(x), axis=1)
    df['前区峰度'] = df[['前区1', '前区2', '前区3', '前区4', '前区5']].apply(lambda x: stats.kurtosis(x), axis=1)
    
    # 前区区间比和区间比之和
    def interval_ratio(row):
        counts = [sum(1 for x in row if 1 <= x <= 12),
                  sum(1 for x in row if 13 <= x <= 24),
                  sum(1 for x in row if 25 <= x <= 35)]
        return f"{counts[0]}:{counts[1]}:{counts[2]}"
    
    df['区间比'] = df[['前区1', '前区2', '前区3', '前区4', '前区5']].apply(interval_ratio, axis=1)
    df['区间比之和'] = df[['前区1', '前区2', '前区3', '前区4', '前区5']].apply(lambda x: 6.5 * sum(1 for i in x if 1 <= i <= 12) + 
                                                                            18.5 * sum(1 for i in x if 13 <= i <= 24) + 
                                                                            30 * sum(1 for i in x if 25 <= i <= 35), axis=1)
    
    # 前区连续数
    df['前区连续数'] = df[['前区1', '前区2', '前区3', '前区4', '前区5']].apply(calculate_consecutive_numbers, axis=1)
    
    # 上期重复数
    def repeated_numbers(current, previous):
        return sum(1 for x in current if x in previous)
    
    df['上期重复数'] = df.apply(lambda row: repeated_numbers(
        [row['前区1'], row['前区2'], row['前区3'], row['前区4'], row['前区5']],
        df.loc[df.index[df.index.get_loc(row.name) + 1] if df.index.get_loc(row.name) + 1 < len(df) else df.index[-1], 
               ['前区1', '前区2', '前区3', '前区4', '前区5']].tolist()
    ) if df.index.get_loc(row.name) + 1 < len(df) else 0, axis=1)

    return df

def main():
    all_data = []
    page = 1
    page_size = 100

    while True:
        try:
            lottery_list = fetch_lottery_data(page, page_size)
            if not lottery_list:
                break
            
            page_data = parse_lottery_data(lottery_list)
            all_data.extend(page_data)
            print(f"已爬取第 {page} 页数据")
            
            if len(lottery_list) < page_size:
                break
            
            page += 1
        except Exception as e:
            print(f"在处理第 {page} 页时发生错误: {e}")
            break

    if not all_data:
        print("未获取到数据，退出程序。")
        return

    try:
        df = pd.DataFrame(all_data, columns=['期号', '开奖日期', '前区1', '前区2', '前区3', '前区4', '前区5', '蓝球1', '蓝球2'])
        
        # 确保所有的前区和蓝球列都是整数类型
        for col in ['前区1', '前区2', '前区3', '前区4', '前区5', '蓝球1', '蓝球2']:
            df[col] = df[col].astype(int)
        
        # 计算分析列
        df = calculate_analysis(df)
        
        # 保存为Excel文件
        df.to_excel('analysisball/dlt/analysis.xlsx', index=False)
        print("数据已保存到 analysis.xlsx")
    except Exception as e:
        print(f"在处理数据时发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
