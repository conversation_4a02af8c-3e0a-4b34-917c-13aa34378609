from random import randint, sample
from bigfunball.fiveplustwo import Logger
from collections import Counter
import sys

sys.stdout = Logger('big1.txt')
with open('rand/7.txt') as f:
    data = f.readlines()
    finallist7 = []
    totalseed = [x for x in range(0, len(data))]
    randomseed7 = sample(totalseed, 7)
    dict1 = dict(Counter(randomseed7))
    # print('randomseed:', randomseed7)
    print('7.txt')
    for i in range(7):
        # print(data[i])
        data[randomseed7[i]] = data[randomseed7[i]].replace('\n', '')
        # print(data[i])
        finallist7.append(data[randomseed7[i]])
    finallist7.sort()
    for i in range(len(finallist7)):
        print(finallist7[i])

with open('rand/77.txt') as f:
    data = f.readlines()
    finallist77 = []
    totalseed = [x for x in range(0, len(data))]
    randomseed77 = sample(totalseed, 7)
    dict2 = dict(Counter(randomseed77))
    # print('randomseed:', randomseed7)
    print('77.txt')
    for i in range(7):
        # print(data[i])
        data[randomseed77[i]] = data[randomseed77[i]].replace('\n', '')
        # print(data[i])
        finallist77.append(data[randomseed77[i]])
    finallist77.sort()
    for i in range(len(finallist77)):
        print(finallist77[i])

with open('rand/777.txt') as f:
    data = f.readlines()
    finallist777 = []
    totalseed = [x for x in range(0, len(data))]
    randomseed777 = sample(totalseed, 7)
    dict3 = dict(Counter(randomseed777))
    # print('randomseed:', randomseed7)
    print('777.txt')
    for i in range(7):
        # print(data[i])
        data[randomseed777[i]] = data[randomseed777[i]].replace('\n', '')
        # print(data[i])
        finallist777.append(data[randomseed777[i]])
    finallist777.sort()
    for i in range(len(finallist777)):
        print(finallist777[i])

with open('rand/7777.txt') as f:
    data = f.readlines()
    finallist7777 = []
    totalseed = [x for x in range(0, len(data))]
    randomseed7777 = sample(totalseed, 7)
    dict4 = dict(Counter(randomseed7777))
    # print('randomseed:', randomseed7)
    print('7777.txt')
    for i in range(7):
        # print(data[i])
        data[randomseed7777[i]] = data[randomseed7777[i]].replace('\n', '')
        # print(data[i])
        finallist7777.append(data[randomseed7777[i]])
    finallist7777.sort()
    for i in range(len(finallist7777)):
        print(finallist7777[i])

    print("dict1: ", {key: value for key, value in dict1.items() if value > 1})
    print("dict2: ", {key: value for key, value in dict2.items() if value > 1})
    print("dict3: ", {key: value for key, value in dict3.items() if value > 1})
    print("dict4: ", {key: value for key, value in dict4.items() if value > 1})
    exit(0)
