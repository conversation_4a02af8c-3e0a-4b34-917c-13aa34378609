import pandas as pd
import numpy as np

def predict_ratio(df, ratio_column, n_periods, top_n):
    recent_data = df.head(n_periods)
    ratios = recent_data[ratio_column].iloc[:min(5, len(recent_data))].tolist()
    conditions = [(recent_data[ratio_column].shift(i) == ratios[i]) for i in range(len(ratios))]
    historical_patterns = recent_data[np.logical_and.reduce(conditions)]
    
    if len(historical_patterns) > 0:
        next_ratios = historical_patterns[ratio_column].shift(-1).dropna()
        if len(next_ratios) > 0:
            value_counts = next_ratios.value_counts()
            predictions = [(ratio, count / len(next_ratios)) for ratio, count in value_counts.items()]
            return sorted(predictions, key=lambda x: x[1], reverse=True)[:top_n], "历史模式", len(historical_patterns)
    
    counts = recent_data[ratio_column].value_counts()
    total = counts.sum()
    predictions = [(ratio, count / total) for ratio, count in counts.items()]
    
    return sorted(predictions, key=lambda x: x[1], reverse=True)[:top_n], "频率预测", 0


def predict_odd_even_ratio(df, n_periods, top_n):
    return predict_ratio(df, '红球奇偶比', n_periods, top_n)

def predict_interval_ratio(df, n_periods, top_n):
    return predict_ratio(df, '红球区间比', n_periods, top_n)

def predict_consecutive_numbers(df, n_periods, top_n):
    return predict_ratio(df, '红球连续数', n_periods, top_n)

def predict_repeated_numbers(df, n_periods, top_n):
    return predict_ratio(df, '上期重复数', n_periods, top_n)

def check_prediction(prediction, actual):
    if actual is not None:
        return "对" if prediction == actual else "错"
    else:
        return "数据尚未公布"

def main(start_period, n_periods):
    df = pd.read_excel('analysisball/ssq/analysis.xlsx')
    df.set_index('期号', inplace=True)
    
    periods = df.index.tolist()
    start_index = periods.index(start_period)
    
    stats = {
        '红球和值': {'正确': 0, '错误': 0},
        '奇偶比': [{'正确': 0, '错误': 0} for _ in range(5)],  # 调整为6
        '区间比': [{'正确': 0, '错误': 0} for _ in range(18)],  # 调整为24
        '红球连续数': [{'正确': 0, '错误': 0} for _ in range(3)],
        '上期重复数': [{'正确': 0, '错误': 0} for _ in range(3)]
    }
    
    for i in range(start_index, -1, -1):
        current_period = periods[i]
        next_period = periods[i - 1] if i > 0 else None
        
        data_to_use = df.iloc[i:min(i + n_periods, len(df))]
        
        if len(data_to_use) < n_periods:
            continue

        odd_even_predictions, odd_even_method, _ = predict_odd_even_ratio(data_to_use, n_periods, 5)  # 调整为6
        interval_predictions, interval_method, _ = predict_interval_ratio(data_to_use, n_periods, 18)  # 调整为24
        consecutive_predictions, consecutive_method, _ = predict_consecutive_numbers(data_to_use, n_periods, 3)
        repeated_predictions, repeated_method, _ = predict_repeated_numbers(data_to_use, n_periods, 3)
        
        # Rest of the code remains the same

        
        actual_data = {
            '红球和值': df.loc[next_period, '红球和值'] if next_period in df.index else None,
            '奇偶比': df.loc[next_period, '红球奇偶比'] if next_period in df.index else None,
            '区间比': df.loc[next_period, '红球区间比'] if next_period in df.index else None,
            '红球连续数': df.loc[next_period, '红球连续数'] if next_period in df.index else None,
            '上期重复数': df.loc[next_period, '上期重复数'] if next_period in df.index else None
        }
        
        print(f"预测 {next_period} 期结果：")
        
        print(f"奇偶比预测 (方法: {odd_even_method}):")
        for idx, (pred, prob) in enumerate(odd_even_predictions):
            result = check_prediction(pred, actual_data['奇偶比'])
            print(f"  {pred}，概率: {prob:.2f} - {result}")
            update_stats(stats, '奇偶比', result, idx)
        
        print(f"区间比预测 (方法: {interval_method}):")
        for idx, (pred, prob) in enumerate(interval_predictions):
            result = check_prediction(pred, actual_data['区间比'])
            print(f"  {pred}，概率: {prob:.2f} - {result}")
            update_stats(stats, '区间比', result, idx)
        
        print(f"红球连续数预测 (方法: {consecutive_method}):")
        for idx, (pred, prob) in enumerate(consecutive_predictions):
            result = check_prediction(pred, actual_data['红球连续数'])
            print(f"  {pred}，概率: {prob:.2f} - {result}")
            update_stats(stats, '红球连续数', result, idx)
        
        print(f"上期重复数预测 (方法: {repeated_method}):")
        for idx, (pred, prob) in enumerate(repeated_predictions):
            result = check_prediction(pred, actual_data['上期重复数'])
            print(f"  {pred}，概率: {prob:.2f} - {result}")
            update_stats(stats, '上期重复数', result, idx)
        
        print("\n")
    
    print_summary(stats)

def update_stats(stats, category, result, idx=None):
    if idx is not None:
        while len(stats[category]) <= idx:
            stats[category].append({'正确': 0, '错误': 0})
    
    if result == "对":
        if idx is None:
            stats[category]['正确'] += 1
        else:
            stats[category][idx]['正确'] += 1
    elif result == "错":
        if idx is None:
            stats[category]['错误'] += 1
        else:
            stats[category][idx]['错误'] += 1

def print_summary(stats):
    print("汇总结果：")
    
    for category in ['奇偶比', '区间比', '红球连续数', '上期重复数']:
        print(f"{category}预测：")
        for idx, stat in enumerate(stats[category]):
            print(f"  第 {idx+1} 行：正确 {stat['正确']} 次，错误 {stat['错误']} 次")

if __name__ == "__main__":
    main(2024001, 20)
    