import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 读取CSV文件
df = pd.read_csv('analysisball/ssq/双色球历史中奖号码.csv')

# 将红球和蓝球转换为整数类型
for col in df.columns[1:]:
    df[col] = df[col].astype(int)

# 1. 计算总和值
df['红球总和'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].sum(axis=1)

# 2. 计算奇偶比
df['红球奇数个数'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(lambda x: sum(i % 2 != 0 for i in x), axis=1)
df['红球偶数个数'] = 6 - df['红球奇数个数']
df['奇偶比'] = df['红球奇数个数'].astype(str) + ':' + df['红球偶数个数'].astype(str)

# 3. 计算区间比
def get_interval(num):
    if 1 <= num <= 11:
        return 0
    elif 12 <= num <= 22:
        return 1
    else:
        return 2

df['区间比'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(lambda row: pd.Series(get_interval(x) for x in row), axis=1).sum(axis=1).map({0: '0:0:6', 1: '0:1:5', 2: '0:2:4', 3: '0:3:3', 4: '1:3:2', 5: '1:4:1', 6: '2:4:0'})

# 4. 计算重复号码
df['重复号码'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(lambda x: len(x) - len(set(x)), axis=1)

# 5. 统计频率
red_freq = pd.Series(df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].values.ravel()).value_counts().sort_index()
blue_freq = df['蓝球'].value_counts().sort_index()

# 6. 计算遗漏值
def calculate_miss_value(series):
    series = pd.Series(series)
    miss_value = pd.Series(0, index=range(1, series.max() + 1))
    for i in range(1, series.max() + 1):
        if i not in series.values:
            miss_value[i] = series.index[-1] - series[series < i].index[-1] if any(series < i) else series.index[-1]
    return miss_value

red_miss = calculate_miss_value(df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].values.ravel())
blue_miss = calculate_miss_value(df['蓝球'])

# 输出统计结果
print("红球总和统计：")
print(df['红球总和'].describe())
print("\n奇偶比统计：")
print(df['奇偶比'].value_counts())
print("\n区间比统计：")
print(df['区间比'].value_counts())
print("\n重复号码统计：")
print(df['重复号码'].value_counts())
print("\n红球出现频率（前10）：")
print(red_freq.head(10))
print("\n蓝球出现频率：")
print(blue_freq)
print("\n红球遗漏值（前10）：")
print(red_miss.head(10))
print("\n蓝球遗漏值：")
print(blue_miss)

# 绘制红球频率直方图
plt.figure(figsize=(12, 6))
red_freq.plot(kind='bar')
plt.title('红球出现频率')
plt.xlabel('号码')
plt.ylabel('频率')
plt.savefig('analysisball/ssq/红球频率直方图.png')
plt.close()

# 绘制蓝球频率直方图
plt.figure(figsize=(8, 6))
blue_freq.plot(kind='bar')
plt.title('蓝球出现频率')
plt.xlabel('号码')
plt.ylabel('频率')
plt.savefig('analysisball/ssq/蓝球频率直方图.png')
plt.close()

print("分析结果已保存到相应的CSV文件和图表中。")
