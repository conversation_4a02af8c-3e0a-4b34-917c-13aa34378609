class Person(object):
    # 有slot，则类只能绑定slot下的属性
    __slots__ = ('_name', '_age', '_gender')

    def __init__(self, name, age):
        self._name = name
        self._age = age

    # 静态方法
    @staticmethod
    def is_vaild():
        print("Good")

    # property有getter和setter方法
    @property
    def name(self):
        return self._name

    @property
    def age(self):
        return self._age

    # @name.setter
    # def name(self, name):
    #     self._name = name

    @age.setter
    def age(self, age):
        self._age = age

    def play(self):
        if self._age <= 18:
            print("Young")
            print(self._name)
        else:
            print("Adult")
            print(self._name)


def main():
    person = Person("Logue", 16)
    person.play()
    person.age = 19
    person._gender = 'male'
    person._is_gay = True


if __name__ == '__main__':
    main()
