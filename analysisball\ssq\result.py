from itertools import combinations

def is_valid_custom_combination(combo):
    # 检查红球连续数为几个
    sorted_combo = sorted(combo)
    consecutive_count = sum(1 for i in range(len(sorted_combo)-1) if sorted_combo[i] + 1 == sorted_combo[i+1])
    if consecutive_count != 0:
        return False
    
    # 检查总和值
    total = sum(combo)
    if not (122 >= total):
        return False

    # # 检查是否存在
    # if 33 not in combo:
    #     return False
    
    # 检查区间比
    interval_counts = [sum(1 for num in combo if 1 <= num <= 11),
                      sum(1 for num in combo if 12 <= num <= 22),
                      sum(1 for num in combo if 23 <= num <= 33)]
    if interval_counts != [1,4,1]:
        return False
    
    # 检查奇偶比
    odd_count = sum(1 for num in combo if num % 2 != 0)
    even_count = 6 - odd_count
    if odd_count != 3 or even_count != 3:
        return False
    
    # 检查上期重复数
    last_draw = {2,19,21,22,28,30}
    if len(set(combo) & last_draw) != 1:
        return False
    
    # 检查尾数
    allowed_tails = {0,1,4,5,7,8}
    present_tails = {num % 10 for num in combo}
    if present_tails != allowed_tails:
        return False

    return True

# 生成所有可能的组合
all_numbers = list(range(1, 34))
valid_custom_combinations = []

for combo in combinations(all_numbers, 6):
    if is_valid_custom_combination(combo):
        valid_custom_combinations.append(combo)

# 打印结果
print("符合条件的组合:")
for valid_combo in valid_custom_combinations:
    sorted_combo = sorted(valid_combo)
    combo_sum = sum(sorted_combo)
    
    # 计算区间比
    interval_counts = [sum(1 for num in sorted_combo if 1 <= num <= 11),
                      sum(1 for num in sorted_combo if 12 <= num <= 22),
                      sum(1 for num in sorted_combo if 23 <= num <= 33)]
    
    # 计算区间比之和
    interval_sum = interval_counts[0] * 6 + interval_counts[1] * 17 + interval_counts[2] * 28

    if interval_sum > combo_sum:
    
        print(f"{sorted_combo}----{combo_sum}----{interval_counts}----{interval_sum}")

    # print(f"{sorted_combo}----{combo_sum}----{interval_counts}----{interval_sum}")

print(f"\n符合条件的组合总数: {len(valid_custom_combinations)}")
