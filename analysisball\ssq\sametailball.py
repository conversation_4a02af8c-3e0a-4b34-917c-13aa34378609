import pandas as pd

# 读取Excel文件
path = 'analysisball/ssq/analysis.xlsx'
df = pd.read_excel(path)

def check_pattern(series):
    # 检查红球号码是否存在尾数相同的情况
    tails = [int(str(num)[-2:]) for num in series[:6]]
    return len(set(tails)) == 1 and min(tails) >= 10

def find_pattern(df):
    n = len(df)
    for i in range(n - 9):  # 需要至少10期来检查两个5期的窗口
        first_window = df.iloc[i:i+5, 1:7]  # 取第一个5期窗口的红球号码
        if any(first_window.apply(check_pattern, axis=1)):
            second_window = df.iloc[i+5:i+10, 1:7]  # 取第二个5期窗口的红球号码
            if any(second_window.apply(check_pattern, axis=1)):
                return df.iloc[i, 0], df.iloc[i+5, 0]
    return None

result = find_pattern(df)

if result:
    print(f"找到符合条件的期号：第一个5期窗口起始于 {result[0]}，第二个5期窗口起始于 {result[1]}")
else:
    print("没有找到符合条件的期号")
