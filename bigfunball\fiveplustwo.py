from random import randrange, randint, sample
import sys
import time


class Logger(object):
    def __init__(self, fileN='Default.log'):
        self.terminal = sys.stdout
        self.log = open(fileN, 'w')

    def write(self, message):
        '''print实际相当于sys.stdout.write'''
        self.terminal.write(message)
        self.log.write(message)
        self.log.flush()

    def flush(self):
        self.log.close()


def display(balls):
    for index, ball in enumerate(balls):
        if index == len(balls) - 2:
            print('|', end='')
        print('%02d' % ball, end='')
    print()


def random_select():
    selectball = []
    red_balls = [x for x in range(1, 36)]
    selected_redballs = []
    selected_redballs = sample(red_balls, 5)
    selected_redballs.sort()
    blue_balls = [y for y in range(1, 13)]
    selected_blueball = sample(blue_balls, 2)
    selected_blueball.sort()
    selectball.extend(selected_redballs)
    selectball.extend(selected_blueball)
    return selectball


def main():
    n = int(input('机选几注：'))
    sys.stdout = Logger('1.txt')
    for _ in range(n):
        display(random_select())


if __name__ == '__main__':
    main()
    # with open('1.txt') as f:
    #     data = f.readlines()
    #     # print(type(data))
    #     print('----------------------')
    #     for i in range(len(data)):
    #         data[i] = data[i].replace('\n', '')
    #     data.sort()
    #     # print(data)
    #     # sys.stdout = Logger('2.txt')
    #     for i in range(len(data)):
    #         print(data[i])
