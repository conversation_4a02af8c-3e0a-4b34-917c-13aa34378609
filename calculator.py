import tkinter as tk
from tkinter import ttk
import math

class Calculator:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("计算器")
        self.window.geometry("320x500")
        self.window.configure(bg='#f0f0f0')
        self.window.resizable(False, False)
        
        # Variables
        self.current = '0'
        self.new_number = True
        self.op_pending = False
        self.last_operator = ''
        self.last_number = 0
        self.memory = 0

        # Display
        self.display_var = tk.StringVar()
        self.display_var.set('0')
        
        # Style configuration
        self.style = {
            'bg_color': '#f5f5f5',
            'button_color': '#ffffff',
            'operator_color': '#f0f0f0',
            'equals_color': '#f0a868',
            'text_color': '#1c1c1c',
            'display_bg': '#ffffff',
            'button_border': '#e5e5e5',
            'display_border': '#d0d0d0'
        }
        
        # Create a frame for display with shadow effect
        display_outer_frame = tk.Frame(self.window, bg=self.style['display_border'])
        display_outer_frame.pack(expand=True, fill='both', padx=12, pady=(20,10))
        
        # Display frame
        display_frame = tk.Frame(
            display_outer_frame,
            bg=self.style['display_bg'],
            highlightbackground=self.style['display_border'],
            highlightthickness=1,
            bd=2,
            relief='solid'
        )
        display_frame.pack(expand=True, fill='both', padx=1, pady=1)
        
        # Display with right-aligned text and proper padding
        display = tk.Label(
            display_frame,
            textvariable=self.display_var,
            anchor='e',
            bg=self.style['display_bg'],
            fg=self.style['text_color'],
            font=('Arial', 36, 'bold'),
            padx=15,
            pady=10
        )
        display.pack(expand=True, fill='both')

        # Buttons frame
        # Separator line
        separator = tk.Frame(self.window, height=1, bg=self.style['display_border'])
        separator.pack(fill='x', padx=12, pady=(5,0))
        
        # Main buttons frame with more padding
        buttons_frame = tk.Frame(self.window, bg=self.style['bg_color'])
        buttons_frame.pack(expand=True, fill='both', padx=10, pady=(8,10))

        # Memory buttons
        memory_buttons = ['MC', 'MR', 'M+', 'M-', 'MS', 'M⌄']
        for i, btn_text in enumerate(memory_buttons):
            btn = tk.Button(
                buttons_frame,
                text=btn_text,
                font=('Arial', 11),
                bg=self.style['operator_color'],
                fg=self.style['text_color'],
                relief='groove',
                borderwidth=0,
                command=lambda x=btn_text: self.memory_operation(x)
            )
            btn.grid(row=0, column=i, padx=2, pady=(0,5), sticky='nsew')
            self.setup_button_hover(btn)

        # Main buttons
        buttons = [
            ['%', 'CE', 'C', '⌫'],
            ['1/x', 'x²', '√x', '÷'],
            ['7', '8', '9', '×'],
            ['4', '5', '6', '-'],
            ['1', '2', '3', '+'],
            ['+/-', '0', '.', '=']
        ]

        for i, row in enumerate(buttons):
            for j, btn_text in enumerate(row):
                # Determine button style
                if btn_text in '0123456789.':
                    bg_color = self.style['button_color']
                elif btn_text == '=':
                    bg_color = self.style['equals_color']
                else:
                    bg_color = self.style['operator_color']
                
                btn = tk.Button(
                    buttons_frame,
                    text=btn_text,
                    font=('Arial', 14, 'bold') if btn_text == '=' else ('Arial', 14),
                    bg=bg_color,
                    fg=self.style['text_color'],
                relief='groove',
                borderwidth=0,
                    command=lambda x=btn_text: self.button_click(x)
                )
                # Custom padding for different button groups
                if i == 0:  # First row (%, CE, C, ⌫)
                    pady = (5,2)
                elif i == 1:  # Second row (1/x, x², √x, ÷)
                    pady = 2
                else:  # Number pad and operators
                    pady = 2
                btn.grid(row=i+1, column=j, padx=2, pady=pady, sticky='nsew')
                self.setup_button_hover(btn)

        # Configure grid weights
        for i in range(6):
            buttons_frame.grid_rowconfigure(i, weight=1)
        for i in range(4):
            buttons_frame.grid_columnconfigure(i, weight=1)

    def button_click(self, value):
        if value in '0123456789.':
            self.handle_number(value)
        elif value in '+-×÷':
            self.handle_operator(value)
        elif value == '=':
            self.calculate()
        elif value == 'C' or value == 'CE':
            self.clear()
        elif value == '⌫':
            self.backspace()
        elif value == '+/-':
            self.toggle_sign()
        elif value == '%':
            self.percentage()
        elif value == 'x²':
            self.square()
        elif value == '√x':
            self.square_root()
        elif value == '1/x':
            self.reciprocal()

    def handle_number(self, value):
        if self.new_number:
            self.current = value
            self.new_number = False
        else:
            if value == '.' and '.' in self.current:
                return
            self.current += value
        self.update_display()

    def handle_operator(self, value):
        if not self.op_pending:
            self.last_number = float(self.current)
        else:
            self.calculate()
        self.last_operator = value
        self.op_pending = True
        self.new_number = True

    def calculate(self):
        if not self.op_pending:
            return
        current = float(self.current)
        if self.last_operator == '+':
            result = self.last_number + current
        elif self.last_operator == '-':
            result = self.last_number - current
        elif self.last_operator == '×':
            result = self.last_number * current
        elif self.last_operator == '÷':
            if current == 0:
                result = 'Error'
            else:
                result = self.last_number / current
        
        if result == 'Error':
            self.current = result
        else:
            self.current = str(result)
            if self.current.endswith('.0'):
                self.current = self.current[:-2]
        
        self.new_number = True
        self.op_pending = False
        self.update_display()

    def clear(self):
        self.current = '0'
        self.new_number = True
        self.op_pending = False
        self.last_operator = ''
        self.last_number = 0
        self.update_display()

    def backspace(self):
        if len(self.current) > 1:
            self.current = self.current[:-1]
        else:
            self.current = '0'
            self.new_number = True
        self.update_display()

    def toggle_sign(self):
        if self.current.startswith('-'):
            self.current = self.current[1:]
        else:
            self.current = '-' + self.current
        self.update_display()

    def percentage(self):
        value = float(self.current)
        self.current = str(value / 100)
        self.update_display()

    def square(self):
        value = float(self.current)
        self.current = str(value ** 2)
        self.update_display()

    def square_root(self):
        value = float(self.current)
        if value < 0:
            self.current = 'Error'
        else:
            self.current = str(math.sqrt(value))
        self.update_display()

    def reciprocal(self):
        value = float(self.current)
        if value == 0:
            self.current = 'Error'
        else:
            self.current = str(1 / value)
        self.update_display()

    def memory_operation(self, operation):
        if operation == 'MC':
            self.memory = 0
        elif operation == 'MR':
            self.current = str(self.memory)
            self.new_number = True
        elif operation == 'M+':
            self.memory += float(self.current)
        elif operation == 'M-':
            self.memory -= float(self.current)
        elif operation == 'MS':
            self.memory = float(self.current)
        self.update_display()

    def update_display(self):
        self.display_var.set(self.current)

    def setup_button_hover(self, button):
        original_color = button.cget('background')
        
        def on_enter(e):
            if original_color == self.style['button_color']:
                button.configure(background='#f0f0f0')
            elif original_color == self.style['operator_color']:
                button.configure(background='#e8e8e8')
            elif original_color == self.style['equals_color']:
                button.configure(background='#e89858')
                
        def on_leave(e):
            button.configure(background=original_color)
            
        button.bind('<Enter>', on_enter)
        button.bind('<Leave>', on_leave)

    def run(self):
        self.window.mainloop()

if __name__ == '__main__':
    calc = Calculator()
    calc.run()
