class Node:
    def __init__(self, data, color="red"):
        self.data = data  # 节点存储的数据
        self.color = color  # 节点颜色，初始为红色
        self.parent = None  # 父节点引用
        self.left = None  # 左子节点引用
        self.right = None  # 右子节点引用
        
class RedBlackTree:
    def __init__(self):
        self.TNULL = Node(0)  # 创建一个 TNULL 节点，用于表示 NIL 节点
        self.TNULL.color = "black"  # NIL 节点永远为黑色
        self.root = self.TNULL  # 根节点初始化为 NIL

    def insert(self, data):
        new_node = Node(data)  # 创建新的节点
        parent = None  # parent 用于记录位置
        current = self.root  # 从根节点开始

        # 确定新节点的插入位置
        while current != self.TNULL:
            parent = current
            if new_node.data < current.data:
                current = current.left
            else:
                current = current.right

        # 设置新节点的 parent 指针
        new_node.parent = parent
        if parent is None:
            self.root = new_node  # 如果树为空，则新节点为根节点
        elif new_node.data < parent.data:
            parent.left = new_node
        else:
            parent.right = new_node

        new_node.left = self.TNULL
        new_node.right = self.TNULL

        # 插入后修复红黑树性质
        self.fix_insert(new_node)

    # 左旋转
    def rotate_left(self, x):
        y = x.right
        x.right = y.left
        if y.left != self.TNULL:
            y.left.parent = x
        y.parent = x.parent
        if x.parent is None:
            self.root = y
        elif x == x.parent.left:
            x.parent.left = y
        else:
            x.parent.right = y
        y.left = x
        x.parent = y

    # 右旋转
    def rotate_right(self, y):
        x = y.left
        y.left = x.right
        if x.right != self.TNULL:
            x.right.parent = y
        x.parent = y.parent
        if y.parent is None:
            self.root = x
        elif y == y.parent.right:
            y.parent.right = x
        else:
            y.parent.left = x
        x.right = y
        y.parent = x

    # 插入操作后，用于修复可能违反红黑树性质的情况
    def fix_insert(self, k):
        while k != self.root and k.parent.color == "red":
            if k.parent == k.parent.parent.right:
                u = k.parent.parent.left
                if u.color == "red":
                    u.color = "black"
                    k.parent.color = "black"
                    k.parent.parent.color = "red"
                    k = k.parent.parent
                else:
                    if k == k.parent.left:
                        k = k.parent
                        self.rotate_right(k)
                    k.parent.color = "black"
                    k.parent.parent.color = "red"
                    self.rotate_left(k.parent.parent)
            else:
                u = k.parent.parent.right
                if u.color == "red":
                    u.color = "black"
                    k.parent.color = "black"
                    k.parent.parent.color = "red"
                    k = k.parent.parent
                else:
                    if k == k.parent.right:
                        k = k.parent
                        self.rotate_left(k)
                    k.parent.color = "black"
                    k.parent.parent.color = "red"
                    self.rotate_right(k.parent.parent)
            if k == self.root:
                break
        self.root.color = "black"

    # 打印整颗红黑树
    def print_tree(self, node, indent, last):
        if node != self.TNULL:
            print(indent, end=' ')
            if last:
                print("R----", end=' ')
                indent += "     "
            else:
                print("L----", end=' ')
                indent += "|    "
            s_color = "RED" if node.color == "red" else "BLACK"
            print(str(node.data) + "(" + s_color + ")")
            self.print_tree(node.left, indent, False)
            self.print_tree(node.right, indent, True)


# 使用示例
if __name__ == "__main__":
    rbt = RedBlackTree()
