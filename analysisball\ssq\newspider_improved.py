import requests
import pandas as pd
import numpy as np
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Alignment, Font
from openpyxl.utils import get_column_letter
import os
import logging
from datetime import datetime
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysisball/ssq/spider.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置参数
CONFIG = {
    'excel_file_path': 'analysisball/ssq/analysis.xlsx',
    'api_url': 'http://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice',
    'lottery_type': 'ssq',
    'page_size': 30,  # 增加获取的记录数，确保不会遗漏数据
    'max_retries': 3,  # 最大重试次数
    'retry_delay': 2,  # 重试延迟（秒）
    'headers': {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
}


def load_existing_data(file_path):
    """
    加载现有数据或创建新的DataFrame
    
    Args:
        file_path: Excel文件路径
        
    Returns:
        tuple: (DataFrame, 最后一期期号)
    """
    try:
        if os.path.exists(file_path):
            existing_df = pd.read_excel(file_path)
            if len(existing_df) > 0 and '期号' in existing_df.columns:
                last_issue = existing_df['期号'].iloc[0]
                logger.info(f"已加载现有数据，最新期号: {last_issue}")
                return existing_df, last_issue
            else:
                logger.warning("现有文件格式不正确，创建新的DataFrame")
        else:
            logger.info(f"文件 {file_path} 不存在，将创建新文件")
        
        # 创建新的DataFrame
        columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球']
        return pd.DataFrame(columns=columns), '00000000'
    
    except Exception as e:
        logger.error(f"加载现有数据时出错: {str(e)}")
        # 出错时也返回一个新的DataFrame
        columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球']
        return pd.DataFrame(columns=columns), '00000000'


def fetch_lottery_data(url, params, headers, max_retries=3, retry_delay=2):
    """
    从API获取彩票数据
    
    Args:
        url: API URL
        params: 请求参数
        headers: 请求头
        max_retries: 最大重试次数
        retry_delay: 重试延迟（秒）
        
    Returns:
        dict: API响应数据
    """
    for attempt in range(max_retries):
        try:
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()  # 如果响应状态码不是200，将引发HTTPError异常
            
            data = response.json()
            if 'result' not in data:
                logger.warning(f"API响应中没有'result'字段: {data}")
                raise ValueError("API响应格式不正确")
                
            logger.info(f"成功获取数据，共 {len(data['result'])} 条记录")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.warning(f"请求失败 (尝试 {attempt+1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
            else:
                logger.error("达到最大重试次数，无法获取数据")
                raise
        except ValueError as e:
            logger.error(f"数据格式错误: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"获取数据时发生未知错误: {str(e)}")
            raise


def extract_new_results(data, last_issue):
    """
    从API响应中提取新的开奖结果
    
    Args:
        data: API响应数据
        last_issue: 最后一期期号
        
    Returns:
        list: 新的开奖结果列表
    """
    try:
        new_results = []
        for item in data['result']:
            # 验证数据完整性
            required_fields = ['code', 'red', 'blue']
            if not all(field in item for field in required_fields):
                logger.warning(f"跳过不完整的数据项: {item}")
                continue
                
            if int(item['code']) > int(last_issue):
                issue = item['code']
                red_balls = item['red'].split(',')
                blue_ball = item['blue']
                
                # 验证红球和蓝球数据
                if len(red_balls) != 6:
                    logger.warning(f"红球数量不正确，期号 {issue}: {red_balls}")
                    continue
                    
                new_results.append([issue] + red_balls + [blue_ball])
                
        logger.info(f"提取了 {len(new_results)} 条新数据")
        return new_results
    except Exception as e:
        logger.error(f"提取新结果时出错: {str(e)}")
        raise


def calculate_statistics(df):
    """
    计算各种统计指标
    
    Args:
        df: 包含开奖数据的DataFrame
        
    Returns:
        DataFrame: 添加了统计指标的DataFrame
    """
    try:
        # 转换红球列为数值类型
        for i in range(1, 7):
            df[f'红球{i}'] = pd.to_numeric(df[f'红球{i}'])
        df['蓝球'] = pd.to_numeric(df['蓝球'])
            
        # 计算红球和值
        df['红球和值'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].sum(axis=1)
        
        # 计算移动平均和指数移动平均
        df['红球和值MA5'] = df['红球和值'].rolling(window=5).mean()
        df['红球和值EMA5'] = df['红球和值'].ewm(span=5).mean()
        
        # 计算红球奇偶比
        df['红球奇偶比'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(
            lambda x: f"{sum(x % 2 != 0)}:{sum(x % 2 == 0)}", axis=1
        )
        
        # 计算统计指标
        df['红球方差'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].var(axis=1)
        df['红球标准差'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].std(axis=1)
        df['红球中位数'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].median(axis=1)
        df['红球Q1'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].quantile(0.25, axis=1)
        df['红球Q3'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].quantile(0.75, axis=1)
        df['红球偏度'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].skew(axis=1)
        df['红球峰度'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].kurtosis(axis=1)
        
        # 计算红球区间比
        df = calculate_interval_ratio(df)
        
        # 计算红球连续数
        df['红球连续数'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(
            lambda x: sum(np.diff(sorted(x)) == 1), axis=1
        )
        
        # 计算与上期重复的红球数
        df = calculate_repeated_numbers(df)
        
        return df
    except Exception as e:
        logger.error(f"计算统计指标时出错: {str(e)}")
        raise


def calculate_interval_ratio(df):
    """
    计算红球区间比
    
    Args:
        df: DataFrame
        
    Returns:
        DataFrame: 添加了区间比的DataFrame
    """
    def get_interval(num):
        if 1 <= num <= 11: return '1-11'
        elif 12 <= num <= 22: return '12-22'
        else: return '23-33'
    
    # 计算每个红球的区间
    for i in range(1, 7):
        df[f'红球{i}区间'] = df[f'红球{i}'].apply(get_interval)
    
    # 计算区间比
    df['红球区间比'] = df[['红球1区间', '红球2区间', '红球3区间', '红球4区间', '红球5区间', '红球6区间']].apply(
        lambda x: f"{sum(x == '1-11')}:{sum(x == '12-22')}:{sum(x == '23-33')}", axis=1
    )
    
    # 计算区间比平均
    def calculate_interval_average(interval_ratio):
        ratios = list(map(int, interval_ratio.split(':')))
        averages = [6, 17, 28]  # 各区间的平均值
        return sum(r * a for r, a in zip(ratios, averages))
    
    df['红球区间比平均'] = df['红球区间比'].apply(calculate_interval_average)
    
    return df


def calculate_repeated_numbers(df):
    """
    计算与上期重复的红球数
    
    Args:
        df: DataFrame
        
    Returns:
        DataFrame: 添加了重复数的DataFrame
    """
    # 初始化重复数列
    df['上期重复数'] = 0
    
    # 如果只有一行数据，直接返回
    if len(df) <= 1:
        return df
    
    # 计算每期与上期的重复数
    for i in range(len(df) - 1):
        current_balls = set(df.iloc[i][['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']])
        next_balls = set(df.iloc[i+1][['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']])
        df.loc[df.index[i], '上期重复数'] = len(current_balls & next_balls)
    
    return df


def save_to_excel(df, file_path):
    """
    将数据保存到Excel文件
    
    Args:
        df: DataFrame
        file_path: Excel文件路径
    """
    try:
        # 格式化输出
        formatted_df = df.copy()
        for col in formatted_df.columns:
            if formatted_df[col].dtype == 'float64':
                formatted_df[col] = formatted_df[col].round(2)
        
        # 创建或加载工作簿
        wb = load_workbook(file_path) if os.path.exists(file_path) else Workbook()
        ws = wb.active
        ws.title = "analysis"
        
        # 清除工作表内容
        ws.delete_rows(1, ws.max_row)
        
        # 写入列名和数据
        for col_num, column_title in enumerate(formatted_df.columns, 1):
            cell = ws.cell(row=1, column=col_num, value=column_title)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center', vertical='center')
        
        for row_num, row_data in enumerate(formatted_df.values, 2):
            for col_num, cell_value in enumerate(row_data, 1):
                cell = ws.cell(row=row_num, column=col_num, value=cell_value)
                cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2) * 1.2
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # 保存 Excel 文件
        wb.save(file_path)
        logger.info(f"数据已保存到 '{file_path}'")
        
    except Exception as e:
        logger.error(f"保存Excel文件时出错: {str(e)}")
        raise


def main():
    """主函数"""
    try:
        start_time = datetime.now()
        logger.info(f"开始运行爬虫，时间: {start_time}")
        
        # 加载现有数据
        existing_df, last_issue = load_existing_data(CONFIG['excel_file_path'])
        
        # 准备API请求参数
        params = {
            "name": CONFIG['lottery_type'],
            "pageNo": 1,
            "pageSize": CONFIG['page_size'],
            "systemType": "PC"
        }
        
        # 获取数据
        data = fetch_lottery_data(
            CONFIG['api_url'], 
            params, 
            CONFIG['headers'],
            CONFIG['max_retries'],
            CONFIG['retry_delay']
        )
        
        # 提取新的中奖号码数据
        new_results = extract_new_results(data, last_issue)
        
        # 如果没有新数据，直接退出
        if not new_results:
            logger.info("没有新数据可用")
            return
        
        # 创建新的DataFrame并合并
        new_df = pd.DataFrame(new_results, columns=['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球'])
        df = pd.concat([new_df, existing_df]).reset_index(drop=True)
        
        # 计算统计指标
        df = calculate_statistics(df)
        
        # 保存到Excel
        save_to_excel(df, CONFIG['excel_file_path'])
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"爬虫运行完成，耗时: {duration:.2f}秒，添加了 {len(new_results)} 条新数据")
        
        print(f"新数据已添加并保存到 '{CONFIG['excel_file_path']}'")
        
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
        print(f"程序运行出错: {str(e)}")


if __name__ == "__main__":
    main()
