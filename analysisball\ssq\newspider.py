import requests
import pandas as pd
import numpy as np
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Alignment, Font
from openpyxl.utils import get_column_letter
import os

# 文件路径
excel_file_path = 'analysisball/ssq/analysis.xlsx'

# 检查文件是否存在，如果不存在则创建一个新的DataFrame
if os.path.exists(excel_file_path):
    existing_df = pd.read_excel(excel_file_path)
    last_issue = existing_df['期号'].iloc[0]
else:
    existing_df = pd.DataFrame(columns=['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球'])
    last_issue = '00000000'

# API URL和请求参数
url = "http://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice"
params = {
    "name": "ssq",
    "pageNo": 1,
    "pageSize": 10,  # 减少获取的记录数
    "systemType": "PC"
}
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

# 发送请求并获取数据
response = requests.get(url, params=params, headers=headers)
if response.status_code != 200:
    print(f"Failed to retrieve data. Status code: {response.status_code}")
    exit()

data = response.json()

# 提取新的中奖号码数据
new_results = []
for item in data['result']:
    if int(item['code']) > int(last_issue):
        issue = item['code']
        red_balls = item['red'].split(',')
        blue_ball = item['blue']
        new_results.append([issue] + red_balls + [blue_ball])

# 如果没有新数据，直接退出
if not new_results:
    print("No new data available.")
    exit()

# 创建新的DataFrame并合并
new_df = pd.DataFrame(new_results, columns=['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球'])
df = pd.concat([new_df, existing_df]).reset_index(drop=True)

# 转换红球列为数值类型
for i in range(1, 7):
    df[f'红球{i}'] = pd.to_numeric(df[f'红球{i}'])

# 计算红球和值
df['红球和值'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].sum(axis=1)

# 反转顺序计算MA5和EMA5
df['红球和值MA5'] = df['红球和值'][::-1].rolling(window=5).mean()[::-1]
df['红球和值EMA5'] = df['红球和值'][::-1].ewm(span=5).mean()[::-1] 

# 计算红球奇偶比
df['红球奇偶比'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(lambda x: f"{sum(x % 2 != 0)}:{sum(x % 2 == 0)}", axis=1)

df['红球方差'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].var(axis=1)
df['红球标准差'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].std(axis=1)
df['红球中位数'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].median(axis=1)
df['红球Q1'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].quantile(0.25, axis=1)
df['红球Q3'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].quantile(0.75, axis=1)
df['红球偏度'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].skew(axis=1)
df['红球峰度'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].kurtosis(axis=1)

# 计算红球区间比
def get_interval(num):
    if 1 <= num <= 11: return '1-11'
    elif 12 <= num <= 22: return '12-22'
    else: return '23-33'

for i in range(1, 7):
    df[f'红球{i}区间'] = df[f'红球{i}'].apply(get_interval)

df['红球区间比'] = df[['红球1区间', '红球2区间', '红球3区间', '红球4区间', '红球5区间', '红球6区间']].apply(
    lambda x: f"{sum(x == '1-11')}:{sum(x == '12-22')}:{sum(x == '23-33')}", axis=1
)

# 计算红球区间比平均
def calculate_interval_average(interval_ratio):
    ratios = list(map(int, interval_ratio.split(':')))
    averages = [6, 17, 28]
    return sum(r * a for r, a in zip(ratios, averages))

df['红球区间比平均'] = df['红球区间比'].apply(calculate_interval_average)

df['红球连续数'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(
    lambda x: sum(np.diff(sorted(x)) == 1), axis=1
)

df['上期重复数'] = df.apply(lambda row: len(set(row[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']]) & 
                                   set(df.loc[df.index[df.index.get_loc(row.name) + 1] if df.index.get_loc(row.name) < len(df) - 1 else df.index[-1], 
                                              ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']])) if df.index.get_loc(row.name) < len(df) - 1 else 0, axis=1)

# 格式化输出
formatted_df = df.copy()
for col in formatted_df.columns:
    if formatted_df[col].dtype == 'float64':
        formatted_df[col] = formatted_df[col].round(2)

# 保存到Excel
wb = load_workbook(excel_file_path) if os.path.exists(excel_file_path) else Workbook()
ws = wb.active
ws.title = "analysis"

# 清除工作表内容
ws.delete_rows(1, ws.max_row)

# 写入列名和数据
for col_num, column_title in enumerate(formatted_df.columns, 1):
    cell = ws.cell(row=1, column=col_num, value=column_title)
    cell.font = Font(bold=True)
    cell.alignment = Alignment(horizontal='center', vertical='center')

for row_num, row_data in enumerate(formatted_df.values, 2):
    for col_num, cell_value in enumerate(row_data, 1):
        cell = ws.cell(row=row_num, column=col_num, value=cell_value)
        cell.alignment = Alignment(horizontal='center', vertical='center')

# 自动调整列宽
for column in ws.columns:
    max_length = 0
    column_letter = get_column_letter(column[0].column)
    for cell in column:
        try:
            if len(str(cell.value)) > max_length:
                max_length = len(str(cell.value))
        except:
            pass
    adjusted_width = (max_length + 2) * 1.2
    ws.column_dimensions[column_letter].width = adjusted_width

# 保存 Excel 文件
wb.save(excel_file_path)

print(f"新数据已添加并保存到 '{excel_file_path}'")
