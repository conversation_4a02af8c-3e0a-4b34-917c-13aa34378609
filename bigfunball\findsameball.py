import re
import sys
from bigfunball.fiveplustwo import Logger
from collections import Counter
from itertools import chain


sys.stdout = Logger('goalball.txt')
with open('big1.txt') as f:
    data = f.readlines()
    for i in range(len(data)):
        data[i] = data[i].replace('\n', '')
        result = re.sub(r"(?<=\w)(?=(?:\w\w)+$)", ",", data[i][:10])
        print(result)

with open('goalball.txt') as f:
    addlist = []
    data = f.readlines()
    for i in range(len(data)):
        data[i] = data[i].replace('\n', '')
        a = data[i].split(',')
        addlist.append(a)
    # print(addlist)

    result = Counter(chain.from_iterable(addlist))
    dict = {}
    for k, v in result.items():
        dict[k] = v
    res = sorted(dict.items(), key=lambda x: x[1], reverse=True)
    print(res)
