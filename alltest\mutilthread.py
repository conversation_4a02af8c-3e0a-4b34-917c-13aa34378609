import codecs
from threading import Thread, Lock
import os
import sys


class TraceLog(Thread):

    def __init__(self, logName):
        super(TraceLog, self).__init__()
        self.logName = logName
        self.lock = Lock()
        self.contexts = []
        self.isFile()

    def isFile(self):
        if not os.path.exists(self.logName):
            with codecs.open(self.logName, 'w') as f:
                f.write("this log name is :{}\n".format(self.logName))
                f.write("start log\n")

    def write(self, context):
        self.contexts.append(context)

    # threading.Lock:它是一个基本的锁对象，每次只能锁定一次，
    # 其余的锁请求，需等待锁释放后才能获取
    # threading.RLock:它代表可重入锁（Reentrant Lock）。
    # 对于可重入锁，在同一个线程中可以对他进行多次锁定，也可以多次释放。
    # 如果使用RLock，那么acquire()和release()方法必须成对出现
    # 使用RLock对象来控制线程安全，当加锁和释放锁出现在不同的作用范围时
    # 通常建议使用finally块来确保在必要时释放锁
    def run(self):
        while 1:
            self.lock.acquire()
            if len(self.contexts) != 0:
                with codecs.open(self.logName, 'a') as f:
                    for context in self.contexts:
                        f.write(context)
                del self.contexts[:]
            self.lock.release()


class Server(object):
    def log(self):
        print("start server")
        for i in range(100):
            print(i)
        print("end server")


if __name__ == '__main__':
    tracelog = TraceLog("main.log")
    tracelog.start()
    sys.stdout = tracelog
    sys.stderr = tracelog
    server = Server()
    server.log()
