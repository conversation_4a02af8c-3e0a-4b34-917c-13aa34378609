import json

input_data = \
{
  "firecrawl_response": [
    {
      "success": 1,
      "status": "completed",
      "completed": 1,
      "total": 1,
      "creditsUsed": 1,
      "expiresAt": "2025-02-21T03:29:18.000Z",
      "data": [
        {
          "markdown": "[Skip to main content](https://python.langchain.com/api_reference/#main-content)\n\nBack to top Ctrl+K\n\n[Docs](https://python.langchain.com/)\n\n*   [GitHub](https://github.com/langchain-ai/langchain \"GitHub\")\n    \n*   [X / Twitter](https://twitter.com/langchainai \"X / Twitter\")\n    \n\nLangChain Python API Reference[#](https://python.langchain.com/api_reference/#langchain-python-api-reference \"Link to this heading\")\n\n=====================================================================================================================================\n\nWelcome to the LangChain Python API reference. This is a reference for all `langchain-x` packages.\n\nFor user guides see [https://python.langchain.com](https://python.langchain.com/)\n.\n\nFor the legacy API reference hosted on ReadTheDocs see [https://api.python.langchain.com/](https://api.python.langchain.com/)\n.\n\nBase packages[#](https://python.langchain.com/api_reference/#base-packages \"Link to this heading\")\n\n---------------------------------------------------------------------------------------------------\n\n**Core**\n\nlangchain-core: 0.3.36\n\n[core/index.html](https://python.langchain.com/api_reference/core/index.html)\n\n**Langchain**\n\nlangchain: 0.3.19\n\n[langchain/index.html](https://python.langchain.com/api_reference/langchain/index.html)\n\n**Text Splitters**\n\nlangchain-text-splitters: 0.3.6\n\n[text\\_splitters/index.html](https://python.langchain.com/api_reference/text_splitters/index.html)\n\n**Community**\n\nlangchain-community: 0.3.17\n\n[community/index.html](https://python.langchain.com/api_reference/community/index.html)\n\n**Experimental**\n\nlangchain-experimental: 0.3.5rc1\n\n[experimental/index.html](https://python.langchain.com/api_reference/experimental/index.html)\n\nIntegrations[#](https://python.langchain.com/api_reference/#integrations \"Link to this heading\")\n\n-------------------------------------------------------------------------------------------------\n\n**OpenAI**\n\nlangchain-openai 0.3.6\n\n[openai/index.html](https://python.langchain.com/api_reference/openai/index.html)\n\n**Anthropic**\n\nlangchain-anthropic 0.3.7\n\n[anthropic/index.html](https://python.langchain.com/api_reference/anthropic/index.html)\n\n**Google VertexAI**\n\nlangchain-google-vertexai 2.0.13\n\n[google\\_vertexai/index.html](https://python.langchain.com/api_reference/google_vertexai/index.html)\n\n**AWS**\n\nlangchain-aws 0.2.13\n\n[aws/index.html](https://python.langchain.com/api_reference/aws/index.html)\n\n**Huggingface**\n\nlangchain-huggingface 0.1.2\n\n[huggingface/index.html](https://python.langchain.com/api_reference/huggingface/index.html)\n\n**MistralAI**\n\nlangchain-mistralai 0.2.6\n\n[mistralai/index.html](https://python.langchain.com/api_reference/mistralai/index.html)\n\nSee the full list of integrations in the Section Navigation.\n\nOn this page",
          "metadata": {
            "docbuild:last-update": "Feb 19, 2025",
            "viewport": [
              "width=device-width, initial-scale=1.0",
              "width=device-width, initial-scale=1",
              "width=device-width, initial-scale=1"
            ],
            "docsearch:version": "",
            "docsearch:language": "en",
            "favicon": "https://python.langchain.com/api_reference/_static/favicon.png",
            "title": "LangChain Python API Reference — 🦜🔗 LangChain  documentation",
            "language": "en",
            "scrapeId": "65d3d585-a79e-4ed5-8ba3-6ec3f996fa1f",
            "sourceURL": "https://python.langchain.com/api_reference/",
            "url": "https://python.langchain.com/api_reference/",
            "statusCode": 200
          }
        },
        {
          "markdown": "[Skip to main content](https://python.langchain.com/api_reference/#main-content)\n\n",
          "metadata": {
            "docbuild:last-update": "Feb 19, 2025",
            "viewport": [
              "width=device-width, initial-scale=1.0",
              "width=device-width, initial-scale=1",
              "width=device-width, initial-scale=1"
            ],
            "docsearch:version": "",
            "docsearch:language": "en",
            "favicon": "https://python.langchain.com/api_reference/_static/favicon.png",
            "title": "LangChain Python API Reference — 🦜🔗 LangChain  documentation",
            "language": "en",
            "scrapeId": "65d3d585-a79e-4ed5-8ba3-6ec3f996fa1f",
            "sourceURL": "https://python.langchain.com/api_reference/",
            "url": "https://python.langchain.com/api_reference/",
            "statusCode": 200
          }
        }
      ]
    }
  ]
}

def main(firecrawl_response: dict) -> dict:
    markdown_contents = []
    
    # 由于 firecrawl_response 已经是列表，直接获取第一个元素
    response = firecrawl_response[0] if firecrawl_response else {}

    # 从 response 中获取 data
    data = response.get("data", [])

    # 使用列表推导式来提取所有非空的 markdown 内容
    markdown_contents = [item["markdown"] for item in data if item.get("markdown")]
    
    return {
        "result": markdown_contents
    }

# 调用函数并获取结果
# print('input data type', type(input_data))
result = main(input_data)
print(result)
# print(type(result))