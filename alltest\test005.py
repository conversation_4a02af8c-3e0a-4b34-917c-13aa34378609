from PIL import Image
import matplotlib.pyplot as plt

img = Image.open('../Day05-DataAugmentation/img/1.jpg')
out1 = img.transpose(Image.FLIP_LEFT_RIGHT)
out2 = img.transpose(Image.FLIP_TOP_BOTTOM)
out3 = img.transpose(Image.ROTATE_90)
out4 = img.transpose(Image.ROTATE_180)
out5 = img.transpose(Image.ROTATE_270)
plt.figure(figsize=(15, 10))
plt.subplot(2,3,1), plt.imshow(img)
plt.axis('off'); plt.title('origin img')
plt.subplot(2,3,2), plt.imshow(out1)
plt.axis('off'); plt.title('output1')
plt.subplot(2,3,3), plt.imshow(out2)
plt.axis('off'); plt.title('output2')
plt.subplot(2,3,4), plt.imshow(out3)
plt.axis('off'); plt.title('output3')
plt.subplot(2,3,5), plt.imshow(out4)
plt.axis('off'); plt.title('output4')
plt.subplot(2,3,6), plt.imshow(out5)
plt.axis('off'); plt.title('output5')
plt.show()
