[Skip to main content](https://python.langchain.com/api_reference/#main-content)
Back to top Ctrl+K
[![🦜🔗 LangChain documentation - Home](https://python.langchain.com/api_reference/_static/wordmark-api.svg) ![🦜🔗 LangChain documentation - Home](https://python.langchain.com/api_reference/_static/wordmark-api-dark.svg)](https://python.langchain.com/api_reference/index.html)
* [Reference](https://python.langchain.com/api_reference/#)
Ctrl+K
[Docs](https://python.langchain.com/)
* [GitHub](https://github.com/langchain-ai/langchain "GitHub")
* [X / Twitter](https://twitter.com/langchainai "X / Twitter")
Ctrl+K
* [Reference](https://python.langchain.com/api_reference/#)
[Docs](https://python.langchain.com/)
* [GitHub](https://github.com/langchain-ai/langchain "GitHub")
* [X / Twitter](https://twitter.com/langchainai "X / Twitter")
Section Navigation
Base packages
* [Core](https://python.langchain.com/api_reference/core/index.html)
* [Langchain](https://python.langchain.com/api_reference/langchain/index.html)
* [Text Splitters](https://python.langchain.com/api_reference/text_splitters/index.html)
* [Community](https://python.langchain.com/api_reference/community/index.html)
* [Experimental](https://python.langchain.com/api_reference/experimental/index.html)
Integrations
* [AI21](https://python.langchain.com/api_reference/ai21/index.html)
* [Anthropic](https://python.langchain.com/api_reference/anthropic/index.html)
* [AstraDB](https://python.langchain.com/api_reference/astradb/index.html)
* [AWS](https://python.langchain.com/api_reference/aws/index.html)
* [Azure Dynamic Sessions](https://python.langchain.com/api_reference/azure_dynamic_sessions/index.html)
* [Cerebras](https://python.langchain.com/api_reference/cerebras/index.html)
* [Chroma](https://python.langchain.com/api_reference/chroma/index.html)
* [Cohere](https://python.langchain.com/api_reference/cohere/index.html)
* [Deepseek](https://python.langchain.com/api_reference/deepseek/index.html)
* [Elasticsearch](https://python.langchain.com/api_reference/elasticsearch/index.html)
* [Exa](https://python.langchain.com/api_reference/exa/index.html)
* [Fireworks](https://python.langchain.com/api_reference/fireworks/index.html)
* [Google Community](https://python.langchain.com/api_reference/google_community/index.html)
* [Google GenAI](https://python.langchain.com/api_reference/google_genai/index.html)
* [Google VertexAI](https://python.langchain.com/api_reference/google_vertexai/index.html)
* [Groq](https://python.langchain.com/api_reference/groq/index.html)
* [Huggingface](https://python.langchain.com/api_reference/huggingface/index.html)
* [IBM](https://python.langchain.com/api_reference/ibm/index.html)
* [Milvus](https://python.langchain.com/api_reference/milvus/index.html)
* [MistralAI](https://python.langchain.com/api_reference/mistralai/index.html)
* [Neo4J](https://python.langchain.com/api_reference/neo4j/index.html)
* [Nomic](https://python.langchain.com/api_reference/nomic/index.html)
* [Nvidia Ai Endpoints](https://python.langchain.com/api_reference/nvidia_ai_endpoints/index.html)
* [Ollama](https://python.langchain.com/api_reference/ollama/index.html)
* [OpenAI](https://python.langchain.com/api_reference/openai/index.html)
* [Pinecone](https://python.langchain.com/api_reference/pinecone/index.html)
* [Postgres](https://python.langchain.com/api_reference/postgres/index.html)
* [Prompty](https://python.langchain.com/api_reference/prompty/index.html)
* [Qdrant](https://python.langchain.com/api_reference/qdrant/index.html)
* [Redis](https://python.langchain.com/api_reference/redis/index.html)
* [Sema4](https://python.langchain.com/api_reference/sema4/index.html)
* [Snowflake](https://python.langchain.com/api_reference/snowflake/index.html)
* [Sqlserver](https://python.langchain.com/api_reference/sqlserver/index.html)
* [Standard Tests](https://python.langchain.com/api_reference/standard_tests/index.html)
* [Together](https://python.langchain.com/api_reference/together/index.html)
* [Unstructured](https://python.langchain.com/api_reference/unstructured/index.html)
* [Upstage](https://python.langchain.com/api_reference/upstage/index.html)
* [VoyageAI](https://python.langchain.com/api_reference/voyageai/index.html)
* [Weaviate](https://python.langchain.com/api_reference/weaviate/index.html)
* [XAI](https://python.langchain.com/api_reference/xai/index.html)
* [](https://python.langchain.com/api_reference/index.html)
* LangChain Python API Reference
LangChain Python API Reference[#](https://python.langchain.com/api_reference/#langchain-python-api-reference "Link to this heading")
=====================================================================================================================================
Welcome to the LangChain Python API reference. This is a reference for all `langchain-x` packages.
For user guides see [https://python.langchain.com](https://python.langchain.com/)
.
For the legacy API reference hosted on ReadTheDocs see [https://api.python.langchain.com/](https://api.python.langchain.com/)
.
Base packages[#](https://python.langchain.com/api_reference/#base-packages "Link to this heading")
---------------------------------------------------------------------------------------------------
**Core**
langchain-core: 0.3.36
[core/index.html](https://python.langchain.com/api_reference/core/index.html)
**Langchain**
langchain: 0.3.19
[langchain/index.html](https://python.langchain.com/api_reference/langchain/index.html)
**Text Splitters**
langchain-text-splitters: 0.3.6
[text_splitters/index.html](https://python.langchain.com/api_reference/text_splitters/index.html)
**Community**
langchain-community: 0.3.17
[community/index.html](https://python.langchain.com/api_reference/community/index.html)
**Experimental**
langchain-experimental: 0.3.5rc1
[experimental/index.html](https://python.langchain.com/api_reference/experimental/index.html)
Integrations[#](https://python.langchain.com/api_reference/#integrations "Link to this heading")
-------------------------------------------------------------------------------------------------
**OpenAI**
langchain-openai 0.3.6
[openai/index.html](https://python.langchain.com/api_reference/openai/index.html)
**Anthropic**
langchain-anthropic 0.3.7
[anthropic/index.html](https://python.langchain.com/api_reference/anthropic/index.html)
**Google VertexAI**
langchain-google-vertexai 2.0.13
[google_vertexai/index.html](https://python.langchain.com/api_reference/google_vertexai/index.html)
**AWS**
langchain-aws 0.2.13
[aws/index.html](https://python.langchain.com/api_reference/aws/index.html)
**Huggingface**
langchain-huggingface 0.1.2
[huggingface/index.html](https://python.langchain.com/api_reference/huggingface/index.html)
**MistralAI**
langchain-mistralai 0.2.6
[mistralai/index.html](https://python.langchain.com/api_reference/mistralai/index.html)
See the full list of integrations in the Section Navigation.
On this page
* [Base packages](https://python.langchain.com/api_reference/#base-packages)
* [Integrations](https://python.langchain.com/api_reference/#integrations)
© Copyright 2023, LangChain Inc.