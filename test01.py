class Fib(object):

    def __init__(self, num):
        self.num = num
        self.a, self.b = 0, 1
        self.idx = 0

    def __iter__(self):
        return self

    def __next__(self):
        if self.idx < self.num:
            self.a, self.b = self.b, self.a+self.b
            self.idx += 1
            return self.a
        raise StopIteration()


if __name__ == '__main__':
    from math import sqrt

    num = int(input('请输入一个正整数: '))
    end = int(sqrt(num))
    print("end: ", end)
    is_prime = True
    for x in range(2, end + 1):
        if num % x == 0:
            is_prime = False
            break
    if is_prime and num != 1:
        print('%d是素数' % num)
        print("is_prime: ", is_prime)
    else:
        print('%d不是素数' % num)
        print("is_prime: ", is_prime)
