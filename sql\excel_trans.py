import pandas as pd

# 设置pandas的浮点显示格式
pd.options.display.float_format = '{:.2f}'.format

# 读取Excel文件的所有sheet名称
xls = pd.ExcelFile('2024.xlsx')
sheet_names = [name for name in xls.sheet_names if name.startswith('三包费用单吨')]

# 创建一个空的DataFrame来存储所有数据
all_data = pd.DataFrame()

for sheet_name in sheet_names:
    df = pd.read_excel(xls, sheet_name=sheet_name, header=[0, 1])

    # 提取所有列名
    columns = df.columns

    # 提取公司名称和公司代码（排除无关值）
    company_names = [col[0] for col in columns[3:] if col[0] not in ['单位', '项目', '验证']]
    company_codes = [col[1] for col in columns[3:] if col[1] not in ['单位', '项目', '验证']]

    # 提取项目、单位和数值
    description = df.iloc[1:, 1].values
    units = df.iloc[1:, 2].values
    values = df.iloc[1:, 3:].values

    # 提取月份信息，用于设置period字段
    period_str = sheet_name.replace('三包费用单吨', '')  # '1月', '2月', etc.
    period = f'2024-{int(period_str[:-1]):02d}'

    # 组装数据
    new_data = []
    for i, company_name in enumerate(company_names):
        company_code = company_codes[i]
        for j, desc in enumerate(description):
            unit = units[j]
            amount = values[j, i]
            new_data.append([company_name, company_code, period, desc, unit, amount])

    # 将数据转换为DataFrame，并追加到all_data中
    new_df = pd.DataFrame(new_data, columns=['company_name', 'company_code', 'period', 'description', 'unit', 'amount'])
    all_data = pd.concat([all_data, new_df], ignore_index=True)

# 保存所有数据到新的Excel文件
all_data.to_excel('new_2024.xlsx', index=False)
