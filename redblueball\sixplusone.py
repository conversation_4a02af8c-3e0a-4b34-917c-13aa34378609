from random import randrange, randint, sample
import sys


class Logger(object):
    def __init__(self, fileN='Default.log'):
        self.terminal = sys.stdout
        self.log = open(fileN, 'w')

    def write(self, message):
        '''print实际相当于sys.stdout.write'''
        self.terminal.write(message)
        self.log.write(message)
        self.log.flush()

    def flush(self):
        self.log.close()


def display(balls):
    for index, ball in enumerate(balls):
        if index == len(balls) - 1:
            print('|', end='')
        print('%02d' % ball, end='')
    print()


def random_select():
    red_balls = [x for x in range(1, 34)]
    selected_balls = []
    selected_balls = sample(red_balls, 6)
    selected_balls.sort()
    selected_balls.append(randint(1, 16))
    return selected_balls


def main():
    n = int(input('机选几注：'))
    sys.stdout = Logger('1.txt')
    for _ in range(n):
        display(random_select())


if __name__ == '__main__':
    main()
    # with open('1.txt') as f:
    #     data = f.readlines()
    #     # print(type(data))
    #     print('----------------------')
    #     for i in range(len(data)):
    #         data[i] = data[i].replace('\n', '')
    #     data.sort()
    #     # print(data)
    #     # sys.stdout = Logger('2.txt')
    #     for i in range(len(data)):
    #         print(data[i])
