import requests
import pandas as pd
import numpy as np
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Alignment, Font, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
import os
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 文件路径
excel_file_path = 'analysisball/ssq/analysis.xlsx'
enhanced_excel_path = 'analysisball/ssq/analysis_enhanced.xlsx'
figures_folder = 'analysisball/ssq/figures'

# 创建图表保存目录
if not os.path.exists(figures_folder):
    os.makedirs(figures_folder)

# 检查文件是否存在，如果不存在则创建一个新的DataFrame
if os.path.exists(excel_file_path):
    existing_df = pd.read_excel(excel_file_path)
    last_issue = existing_df['期号'].iloc[0]
else:
    existing_df = pd.DataFrame(columns=['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球'])
    last_issue = '00000000'

# API URL和请求参数
url = "http://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice"
params = {
    "name": "ssq",
    "pageNo": 1,
    "pageSize": 100,  # 增加获取的记录数，确保有足够历史数据用于分析
    "systemType": "PC"
}
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

# 发送请求并获取数据
try:
    response = requests.get(url, params=params, headers=headers, timeout=10)
    response.raise_for_status()  # 检查HTTP错误
    data = response.json()
except Exception as e:
    print(f"Failed to retrieve data: {str(e)}")
    exit()

# 提取新的中奖号码数据
new_results = []
for item in data['result']:
    if int(item['code']) > int(last_issue):
        issue = item['code']
        red_balls = item['red'].split(',')
        blue_ball = item['blue']
        date = item['date']  # 获取开奖日期
        new_results.append([issue, date] + red_balls + [blue_ball])

# 如果没有新数据，直接退出
if not new_results:
    print("No new data available.")
    exit()

# 创建新的DataFrame并合并
new_df = pd.DataFrame(new_results, columns=['期号', '开奖日期', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球'])

# 检查现有数据中是否有开奖日期列
if '开奖日期' not in existing_df.columns and not existing_df.empty:
    # 如果没有，添加一个默认值列
    existing_df['开奖日期'] = 'Unknown'
    # 调整列的顺序
    existing_df = existing_df[['期号', '开奖日期'] + [col for col in existing_df.columns if col not in ['期号', '开奖日期']]]

df = pd.concat([new_df, existing_df]).reset_index(drop=True)

# 转换红球和蓝球列为数值类型
for i in range(1, 7):
    df[f'红球{i}'] = pd.to_numeric(df[f'红球{i}'])
df['蓝球'] = pd.to_numeric(df['蓝球'])

# 基础特征计算
# 1. 红球和值和平均值
df['红球和值'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].sum(axis=1)
df['红球平均值'] = df['红球和值'] / 6

# 2. 移动平均线和指数移动平均线 (更多周期)
for window in [5, 10, 20]:
    df[f'红球和值MA{window}'] = df['红球和值'][::-1].rolling(window=window).mean()[::-1]
    df[f'红球和值EMA{window}'] = df['红球和值'][::-1].ewm(span=window).mean()[::-1]

# 3. 红球奇偶比
df['红球奇数个数'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(lambda x: sum(n % 2 != 0 for n in x), axis=1)
df['红球偶数个数'] = 6 - df['红球奇数个数']
df['红球奇偶比'] = df.apply(lambda x: f"{x['红球奇数个数']}:{x['红球偶数个数']}", axis=1)

# 4. 红球大小比(大于等于17为大)
df['红球大数个数'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(lambda x: sum(n >= 17 for n in x), axis=1)
df['红球小数个数'] = 6 - df['红球大数个数']
df['红球大小比'] = df.apply(lambda x: f"{x['红球大数个数']}:{x['红球小数个数']}", axis=1)

# 5. 区间分布计算
def get_interval(num):
    if 1 <= num <= 11: return '1-11'
    elif 12 <= num <= 22: return '12-22'
    else: return '23-33'

for i in range(1, 7):
    df[f'红球{i}区间'] = df[f'红球{i}'].apply(get_interval)

interval_columns = [f'红球{i}区间' for i in range(1, 7)]
df['红球区间1个数'] = df[interval_columns].apply(lambda x: sum(interval == '1-11' for interval in x), axis=1)
df['红球区间2个数'] = df[interval_columns].apply(lambda x: sum(interval == '12-22' for interval in x), axis=1)
df['红球区间3个数'] = df[interval_columns].apply(lambda x: sum(interval == '23-33' for interval in x), axis=1)
df['红球区间比'] = df.apply(lambda x: f"{x['红球区间1个数']}:{x['红球区间2个数']}:{x['红球区间3个数']}", axis=1)

# 6. 红球区间比加权平均
def calculate_interval_average(row):
    return row['红球区间1个数'] * 6 + row['红球区间2个数'] * 17 + row['红球区间3个数'] * 28

df['红球区间比平均'] = df.apply(calculate_interval_average, axis=1)

# 7. 红球尾数特征
for i in range(1, 7):
    df[f'红球{i}尾数'] = df[f'红球{i}'] % 10

# 计算尾数频率
for tail in range(10):
    df[f'尾数{tail}出现次数'] = df[[f'红球{i}尾数' for i in range(1, 7)]].apply(
        lambda x: sum(n == tail for n in x), axis=1
    )

df['尾数和值'] = df[[f'红球{i}尾数' for i in range(1, 7)]].sum(axis=1)

# 8. 高级统计特征
df['红球方差'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].var(axis=1)
df['红球标准差'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].std(axis=1)
df['红球中位数'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].median(axis=1)
df['红球Q1'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].quantile(0.25, axis=1)
df['红球Q3'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].quantile(0.75, axis=1)
df['红球IQR'] = df['红球Q3'] - df['红球Q1']  # 四分位距
df['红球偏度'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].skew(axis=1)
df['红球峰度'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].kurtosis(axis=1)
df['红球极差'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].max(axis=1) - df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].min(axis=1)

# 9. 连号特征
df['红球连续数'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(
    lambda x: sum(np.diff(sorted(x)) == 1), axis=1
)

# 更精细的连号分析
def count_consecutive_groups(numbers):
    sorted_nums = sorted(numbers)
    groups = []
    current_group = [sorted_nums[0]]
    
    for i in range(1, len(sorted_nums)):
        if sorted_nums[i] == sorted_nums[i-1] + 1:
            current_group.append(sorted_nums[i])
        else:
            if len(current_group) > 1:
                groups.append(current_group)
            current_group = [sorted_nums[i]]
    
    if len(current_group) > 1:
        groups.append(current_group)
    
    return groups

df['红球连号分组'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(
    lambda x: count_consecutive_groups(x), axis=1
)
df['红球连号组数'] = df['红球连号分组'].apply(len)
df['最长连号长度'] = df['红球连号分组'].apply(lambda groups: max([len(g) for g in groups]) if groups else 0)

# 10. 号码重复特征
df['上期重复数'] = df.apply(
    lambda row: len(set(row[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']]) & 
                  set(df.loc[df.index[df.index.get_loc(row.name) + 1] if df.index.get_loc(row.name) < len(df) - 1 else df.index[-1], 
                           ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']])) if df.index.get_loc(row.name) < len(df) - 1 else 0, 
    axis=1
)

# 扩展为更多期重复情况
for periods in [2, 3, 5]:
    df[f'前{periods}期重复数'] = df.apply(
        lambda row: len(set(row[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']]) & 
                      set().union(*[set(df.loc[df.index[min(df.index.get_loc(row.name) + i, len(df) - 1)], 
                                         ['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']]) 
                                  for i in range(1, periods + 1) 
                                  if df.index.get_loc(row.name) + i < len(df)])) if df.index.get_loc(row.name) < len(df) - 1 else 0,
        axis=1
    )

# 11. 蓝球特征
df['蓝球奇偶'] = df['蓝球'].apply(lambda x: '奇' if x % 2 != 0 else '偶')
df['蓝球大小'] = df['蓝球'].apply(lambda x: '大' if x > 8 else '小')
df['蓝球尾数'] = df['蓝球'] % 10

# 12. 兴趣特征 - 斐波那契序列
fibonacci = set([1, 2, 3, 5, 8, 13, 21])
df['斐波那契数个数'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(
    lambda x: sum(n in fibonacci for n in x), axis=1
)

# 13. 质数个数
primes = set([2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31])
df['质数个数'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(
    lambda x: sum(n in primes for n in x), axis=1
)

# 14. 和值趋势
df['和值增减'] = df['红球和值'].diff(-1)  # 与上一期比较
df['和值增减比例'] = df['和值增减'] / df['红球和值'].shift(-1)

# 15. 热门号码分析
if len(df) >= 10:
    recent_10_draws = df.iloc[:10]
    hot_numbers = []
    for i in range(1, 34):
        count = sum((recent_10_draws[f'红球{j}'] == i).sum() for j in range(1, 7))
        if count >= 2:
            hot_numbers.append(i)
    
    df['热门号码个数'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(
        lambda x: sum(n in hot_numbers for n in x), axis=1
    )

# 16. AC值 (Arithmetic Complexity) - 号码复杂度
def calculate_ac(numbers):
    diffs = []
    sorted_nums = sorted(numbers)
    for i in range(len(sorted_nums)-1):
        for j in range(i+1, len(sorted_nums)):
            diffs.append(sorted_nums[j] - sorted_nums[i])
    return len(set(diffs))

df['AC值'] = df[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']].apply(
    lambda x: calculate_ac(x), axis=1
)

# 数据可视化分析
# 只对前100期数据进行可视化分析，避免图表过于复杂
analysis_df = df.head(100)

# 1. 红球和值分布与趋势
plt.figure(figsize=(12, 6))
plt.plot(analysis_df['期号'][:30], analysis_df['红球和值'][:30], marker='o', label='和值')
plt.plot(analysis_df['期号'][:30], analysis_df['红球和值MA5'][:30], label='5期移动平均')
plt.plot(analysis_df['期号'][:30], analysis_df['红球和值MA10'][:30], label='10期移动平均')
plt.title('红球和值趋势图')
plt.xlabel('期号')
plt.ylabel('和值')
plt.legend()
plt.grid(True)
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig(f'{figures_folder}/红球和值趋势.png')
plt.close()

# 2. 红球区间比分布
interval_counts = analysis_df[['红球区间1个数', '红球区间2个数', '红球区间3个数']].sum()
plt.figure(figsize=(10, 6))
plt.bar(['区间1-11', '区间12-22', '区间23-33'], interval_counts)
plt.title('红球区间比分布')
plt.ylabel('出现次数')
plt.grid(axis='y')
plt.savefig(f'{figures_folder}/红球区间分布.png')
plt.close()

# 3. 奇偶比分布
odd_even_counts = analysis_df['红球奇偶比'].value_counts()
plt.figure(figsize=(10, 6))
plt.bar(odd_even_counts.index, odd_even_counts.values)
plt.title('红球奇偶比分布')
plt.ylabel('出现次数')
plt.grid(axis='y')
plt.savefig(f'{figures_folder}/奇偶比分布.png')
plt.close()

# 4. 尾数分布热图
tail_data = analysis_df[[f'尾数{i}出现次数' for i in range(10)]].mean()
plt.figure(figsize=(10, 6))
plt.bar(range(10), tail_data)
plt.title('红球尾数分布')
plt.xlabel('尾数')
plt.ylabel('平均出现次数')
plt.xticks(range(10))
plt.grid(axis='y')
plt.savefig(f'{figures_folder}/尾数分布.png')
plt.close()

# 5. 红球重复数分析
repeat_data = analysis_df[['上期重复数', '前2期重复数', '前3期重复数', '前5期重复数']].mean()
plt.figure(figsize=(10, 6))
plt.bar(repeat_data.index, repeat_data.values)
plt.title('红球重复数平均值')
plt.ylabel('平均重复数')
plt.grid(axis='y')
plt.savefig(f'{figures_folder}/重复数分析.png')
plt.close()

# 6. 连号分析
consecutive_data = analysis_df['红球连续数'].value_counts().sort_index()
plt.figure(figsize=(10, 6))
plt.bar(consecutive_data.index, consecutive_data.values)
plt.title('红球连续数分布')
plt.xlabel('连续数')
plt.ylabel('出现次数')
plt.xticks(range(max(consecutive_data.index)+1))
plt.grid(axis='y')
plt.savefig(f'{figures_folder}/连号分析.png')
plt.close()

# 7. 蓝球分布
blue_counts = analysis_df['蓝球'].value_counts().sort_index()
plt.figure(figsize=(12, 6))
plt.bar(blue_counts.index, blue_counts.values)
plt.title('蓝球分布情况')
plt.xlabel('蓝球号码')
plt.ylabel('出现次数')
plt.xticks(range(1, 17))
plt.grid(axis='y')
plt.savefig(f'{figures_folder}/蓝球分布.png')
plt.close()

# 相关性分析
# 选择数值型列进行相关性分析
numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
# 排除一些不需要进行相关性分析的列
exclude_cols = [f'红球{i}' for i in range(1, 7)] + [f'红球{i}尾数' for i in range(1, 7)] + [f'尾数{i}出现次数' for i in range(10)]
corr_cols = [col for col in numeric_cols if col not in exclude_cols and '连号分组' not in col]

if len(corr_cols) > 1:  # 确保有足够的列进行相关性分析
    corr_matrix = df[corr_cols].corr()
    
    # 选择相关系数绝对值大于0.5的特征对
    high_corr = corr_matrix.where(np.abs(corr_matrix) > 0.5).stack().reset_index()
    high_corr = high_corr[high_corr['level_0'] != high_corr['level_1']]
    high_corr.columns = ['Feature 1', 'Feature 2', 'Correlation']
    
    # 绘制热图
    plt.figure(figsize=(16, 14))
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    sns.heatmap(corr_matrix, mask=mask, annot=False, cmap='coolwarm', vmin=-1, vmax=1, linewidths=0.5)
    plt.title('特征相关性热图')
    plt.tight_layout()
    plt.savefig(f'{figures_folder}/相关性热图.png')
    plt.close()

# 对上述计算的特征进行归一化和降维，为机器学习做准备
if len(df) >= 30:  # 确保有足够的样本
    # 选择用于PCA的数值型特征
    pca_features = [
        '红球和值', '红球平均值', '红球方差', '红球标准差', '红球偏度', '红球峰度',
        '红球奇数个数', '红球大数个数', '红球区间1个数', '红球区间2个数', '红球区间3个数',
        '红球连续数', '上期重复数', 'AC值'
    ]
    
    available_features = [col for col in pca_features if col in df.columns]
    
    if len(available_features) >= 5:  # 至少需要5个特征才能进行有意义的PCA
        # 标准化数据
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(df[available_features].fillna(0))
        
        # PCA降维
        pca = PCA(n_components=2)
        principal_components = pca.fit_transform(scaled_data)
        
        # 创建带有主成分的DataFrame
        pca_df = pd.DataFrame(data=principal_components, columns=['PC1', 'PC2'])
        
        # 只对前50期数据进行可视化
        plt.figure(figsize=(10, 8))
        plt.scatter(pca_df['PC1'][:50], pca_df['PC2'][:50], alpha=0.7)
        
        # 添加标签
        for i in range(min(50, len(df))):
            plt.annotate(df['期号'].iloc[i], (pca_df['PC1'].iloc[i], pca_df['PC2'].iloc[i]), fontsize=8)
        
        plt.title('主成分分析 (PCA) 可视化')
        plt.xlabel(f'主成分1 (解释方差: {pca.explained_variance_ratio_[0]:.2f})')
        plt.ylabel(f'主成分2 (解释方差: {pca.explained_variance_ratio_[1]:.2f})')
        plt.grid(True)
        plt.savefig(f'{figures_folder}/PCA分析.png')
        plt.close()

# 特征工程总结
print("\n===== 特征工程完成 =====")
print(f"生成了 {len(df.columns)} 个特征")
print(f"保存了 {len(os.listdir(figures_folder))} 张分析图表到 {figures_folder} 目录")

# 保存处理后的数据
# 先截断非常大的值，以便于Excel正确显示
for col in df.columns:
    if df[col].dtype == 'float64':
        df[col] = df[col].round(3)

# 创建一个新的Excel文件保存增强后的数据
enhanced_wb = Workbook()
enhanced_ws = enhanced_wb.active
enhanced_ws.title = "enhanced_analysis"

# 写入列名
for col_num, column_title in enumerate(df.columns, 1):
    cell = enhanced_ws.cell(row=1, column=col_num, value=column_title)
    cell.font = Font(bold=True)
    cell.alignment = Alignment(horizontal='center', vertical='center')
    # 创建一个好看的标题栏
    cell.fill = PatternFill(start_color="B7DEE8", end_color="B7DEE8", fill_type="solid")
    cell.border = Border(
        left=Side(style='thin'), 
        right=Side(style='thin'), 
        top=Side(style='thin'), 
        bottom=Side(style='thin')
    )

# 写入数据
for row_num, row_data in enumerate(df.values, 2):
    for col_num, cell_value in enumerate(row_data, 1):
        # 处理列表类型的数据（例如连号分组）
        if isinstance(cell_value, list):
            cell_value = str(cell_value)
        
        cell = enhanced_ws.cell(row=row_num, column=col_num, value=cell_value)
        cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # 为不同类型的数据添加不同的背景色
        if col_num <= 9:  # 期号和红蓝球原始数据
            pass
        elif "和值" in df.columns[col_num-1]:
            if isinstance(cell_value, (int, float)) and cell_value > 100:
                cell.fill = PatternFill(start_color="FFD700", end_color="FFD700", fill_type="solid")
        elif "奇偶比" in df.columns[col_num-1] and cell_value == "3:3":
            cell.fill = PatternFill(start_color="98FB98", end_color="98FB98", fill_type="solid")
        elif "上期重复数" in df.columns[col_num-1] and isinstance(cell_value, (int, float)) and cell_value >= 2:
            cell.fill = PatternFill(start_color="FFA07A", end_color="FFA07A", fill_type="solid")

# 自动调整列宽
for column in enhanced_ws.columns:
    max_length = 0
    column_letter = get_column_letter(column[0].column)
    for cell in column:
        try:
            if len(str(cell.value)) > max_length:
                max_length = len(str(cell.value))
        except:
            pass
    adjusted_width = (max_length + 2) * 1.2
    enhanced_ws.column_dimensions[column_letter].width = adjusted_width

# 创建统计摘要工作表
stats_ws = enhanced_wb.create_sheet(title="统计摘要")

# 各种统计摘要
summary_data = [
    ["更新时间", datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
    ["共分析期数", len(df)],
    ["首期期号", df['期号'].iloc[-1]],
    ["末期期号", df['期号'].iloc[0]],
    ["红球和值平均", df['红球和值'].mean()],
    ["和值中位数", df['红球和值'].median()],
    ["和值最大值", df['红球和值'].max()],
    ["和值最小值", df['红球和值'].min()],
    ["标准差平均", df['红球标准差'].mean()],
    ["连号期数百分比", f"{(df['红球连续数'] > 0).mean() * 100:.2f}%"],
    ["热门奇偶比", df['红球奇偶比'].value_counts().index[0]],
    ["热门区间比", df['红球区间比'].value_counts().index[0]]
]

for row_num, (key, value) in enumerate(summary_data, 1):
    stats_ws.cell(row=row_num, column=1, value=key).font = Font(bold=True)
    stats_ws.cell(row=row_num, column=2, value=value)

# 奇偶比统计
stats_ws.cell(row=len(summary_data)+2, column=1, value="奇偶比统计").font = Font(bold=True)
odd_even_counts = df['红球奇偶比'].value_counts()
for row_num, (ratio, count) in enumerate(odd_even_counts.items(), len(summary_data)+3):
    stats_ws.cell(row=row_num, column=1, value=ratio)
