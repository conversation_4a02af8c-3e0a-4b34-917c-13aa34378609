* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

body {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f0f2f5;
}

.container {
    background-color: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

h1 {
    margin-bottom: 1.5rem;
    color: #333;
}

.counter {
    margin: 2rem 0;
}

#count {
    font-size: 6rem;
    color: #1890ff;
    font-weight: bold;
}

.buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.btn {
    padding: 0.5rem 2rem;
    font-size: 1.5rem;
    border: none;
    border-radius: 5px;
    color: white;
    cursor: pointer;
    transition: opacity 0.2s;
}

.btn:hover {
    opacity: 0.8;
}

.decrease {
    background-color: #ff4d4f;
}

.reset {
    background-color: #666;
}

.increase {
    background-color: #52c41a;
}
