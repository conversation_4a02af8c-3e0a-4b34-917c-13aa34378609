# Python回滚脚本
# 作者：Augment Agent
# 用途：如果升级出现问题，回滚到Python 3.10

param(
    [Parameter(Mandatory=$true)]
    [string]$BackupDir
)

Write-Host "=== Python回滚脚本 ===" -ForegroundColor Red
Write-Host "当前时间: $(Get-Date)" -ForegroundColor Yellow

# 检查备份目录是否存在
if (-not (Test-Path $BackupDir)) {
    Write-Host "错误：备份目录 '$BackupDir' 不存在！" -ForegroundColor Red
    exit 1
}

Write-Host "使用备份目录: $BackupDir" -ForegroundColor Yellow

# 步骤1：显示当前状态
Write-Host "`n步骤1：当前状态..." -ForegroundColor Cyan
Write-Host "当前Python版本："
python --version

# 步骤2：确认回滚
Write-Host "`n步骤2：确认回滚..." -ForegroundColor Cyan
$confirmation = Read-Host "确定要回滚到Python 3.10吗？(y/N)"

if ($confirmation -eq 'y' -or $confirmation -eq 'Y') {
    Write-Host "`n开始回滚..." -ForegroundColor Yellow
    
    # 步骤3：回滚Python版本
    Write-Host "正在回滚Python到3.10..."
    conda install python=3.10 -y
    
    # 步骤4：验证回滚结果
    Write-Host "`n步骤4：验证回滚结果..." -ForegroundColor Cyan
    Write-Host "回滚后的Python版本："
    python --version
    
    # 步骤5：测试基本功能
    Write-Host "`n步骤5：测试基本功能..." -ForegroundColor Cyan
    python -c "import sys; print(f'Python {sys.version}'); print('回滚成功')"
    
    Write-Host "`n=== 回滚完成！ ===" -ForegroundColor Green
    
} else {
    Write-Host "回滚已取消" -ForegroundColor Yellow
}

Write-Host "`n脚本执行完成！" -ForegroundColor Green
