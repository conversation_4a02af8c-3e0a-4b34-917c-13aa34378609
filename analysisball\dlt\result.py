from itertools import combinations

def is_valid_custom_combination(combo):
    # 检查上期重复数：要求没有重复
    last_draw = {16, 18, 25, 26, 33}
    if len(set(combo) & last_draw) != 0:
        return False
    
    # 检查总和值：在68.5到73.5之间
    total = sum(combo)
    if total <= 68.5 or total >= 73.5:
        return False
    
    # 检查区间比：3:2:0
    interval_counts = [sum(1 for num in combo if 1 <= num <= 12),
                       sum(1 for num in combo if 13 <= num <= 24),
                       sum(1 for num in combo if 25 <= num <= 35)]
    if interval_counts != [3, 2, 0]:
        return False
    
    # 检查奇偶比：2:3 (2个奇数，3个偶数)
    even_count = sum(1 for num in combo if num % 2 == 0)
    odd_count = 5 - even_count
    if even_count != 3 or odd_count != 2:
        return False
    
    # 检查红球连续数为1
    sorted_combo = sorted(combo)
    consecutive_count = sum(1 for i in range(len(sorted_combo)-1) if sorted_combo[i] + 1 == sorted_combo[i+1])
    if consecutive_count != 1:
        return False

    return True

# 生成所有可能的组合
all_numbers = list(range(1, 36))  # 大乐透前区号码范围是1到35
valid_custom_combinations = []

for combo in combinations(all_numbers, 5):  # 大乐透前区选择5个号码
    if is_valid_custom_combination(combo):
        valid_custom_combinations.append(combo)

# 打印结果
print("符合条件的组合:")
for valid_combo in valid_custom_combinations:
    print(sorted(valid_combo))

print(f"\n符合条件的组合总数: {len(valid_custom_combinations)}")
