import pygame
import sys

# Initialize pygame
pygame.init()

# Game constants
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Colors
SKY_BLUE = (107, 140, 255)
BROWN = (139, 69, 19)
RED = (255, 0, 0)
GREEN = (0, 255, 0)

# Create the game window
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Mario Style Platformer")
clock = pygame.time.Clock()

# Player class
class Player(pygame.sprite.Sprite):
    def __init__(self, x, y):
        super().__init__()
        self.image = pygame.Surface((30, 50))
        self.image.fill(RED)
        self.rect = self.image.get_rect()
        self.rect.x = x
        self.rect.y = y
        self.velocity_x = 0
        self.velocity_y = 0
        self.on_ground = False
        
    def update(self):
        # Gravity
        self.velocity_y += 0.8
        
        # Move horizontally
        self.rect.x += self.velocity_x
        
        # Check for horizontal collisions
        block_hit_list = pygame.sprite.spritecollide(self, platforms, False)
        for block in block_hit_list:
            if self.velocity_x > 0:
                self.rect.right = block.rect.left
            elif self.velocity_x < 0:
                self.rect.left = block.rect.right
        
        # Move vertically
        self.rect.y += self.velocity_y
        
        # Check for vertical collisions
        block_hit_list = pygame.sprite.spritecollide(self, platforms, False)
        self.on_ground = False
        for block in block_hit_list:
            if self.velocity_y > 0:
                self.rect.bottom = block.rect.top
                self.on_ground = True
            elif self.velocity_y < 0:
                self.rect.top = block.rect.bottom
            self.velocity_y = 0
            
    def jump(self):
        if self.on_ground:
            self.velocity_y = -16
            
    def go_left(self):
        self.velocity_x = -5
        
    def go_right(self):
        self.velocity_x = 5
        
    def stop(self):
        self.velocity_x = 0

# Platform class        
class Platform(pygame.sprite.Sprite):
    def __init__(self, x, y, width, height):
        super().__init__()
        self.image = pygame.Surface((width, height))
        self.image.fill(BROWN)
        self.rect = self.image.get_rect()
        self.rect.x = x
        self.rect.y = y

# Enemy class
class Enemy(pygame.sprite.Sprite):
    def __init__(self, x, y, min_x, max_x):
        super().__init__()
        self.image = pygame.Surface((30, 30))
        self.image.fill(GREEN)
        self.rect = self.image.get_rect()
        self.rect.x = x
        self.rect.y = y
        self.min_x = min_x
        self.max_x = max_x
        self.velocity_x = 2
        
    def update(self):
        self.rect.x += self.velocity_x
        
        # Change direction if reaching the boundary
        if self.rect.x <= self.min_x or self.rect.x >= self.max_x:
            self.velocity_x *= -1

# Create sprite groups
all_sprites = pygame.sprite.Group()
platforms = pygame.sprite.Group()
enemies = pygame.sprite.Group()

# Create player
player = Player(100, 300)
all_sprites.add(player)

# Create platforms
platform_list = [
    (0, 550, 800, 50),      # Ground
    (100, 400, 150, 30),    # Platform 1
    (300, 300, 150, 30),    # Platform 2
    (500, 200, 150, 30),    # Platform 3
]

for platform in platform_list:
    p = Platform(*platform)
    all_sprites.add(p)
    platforms.add(p)

# Create enemies
enemy_list = [
    (150, 370, 100, 250),   # Enemy on platform 1
    (350, 270, 300, 450),   # Enemy on platform 2
]

for enemy in enemy_list:
    e = Enemy(*enemy)
    all_sprites.add(e)
    enemies.add(e)

# Game loop
running = True
while running:
    # Keep loop running at the right speed
    clock.tick(FPS)
    
    # Process events
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
        elif event.type == pygame.KEYDOWN:
            if event.key == pygame.K_SPACE:
                player.jump()
    
    # Get keyboard state
    keys = pygame.key.get_pressed()
    if keys[pygame.K_LEFT]:
        player.go_left()
    elif keys[pygame.K_RIGHT]:
        player.go_right()
    else:
        player.stop()
    
    # Update all sprites
    all_sprites.update()
    
    # Check for player-enemy collisions
    enemy_hits = pygame.sprite.spritecollide(player, enemies, False)
    if enemy_hits:
        if player.velocity_y > 0 and player.rect.bottom < enemy_hits[0].rect.top + 15:
            # Player jumps on enemy
            enemy_hits[0].kill()
        else:
            # Player gets hit by enemy
            print("Game Over!")
            running = False
    
    # Draw everything
    screen.fill(SKY_BLUE)
    all_sprites.draw(screen)
    
    # Display the frame
    pygame.display.flip()

# Quit the game
pygame.quit()
sys.exit()
