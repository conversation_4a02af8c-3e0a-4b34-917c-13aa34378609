# 简单选择排序
def select_sort(items, comp=lambda x, y: x < y):
    items = items[:]
    for i in range(len(items) - 1):
        min_index = i
        for j in range(i+1, len(items)):
            if comp(items[j], items[min_index]):
                min_index = j
        items[i], items[min_index] = items[min_index], items[i]
    return items


# 冒泡排序
def bubble_sort(items, comp=lambda x, y: x > y):
    items = items[:]
    for i in range(len(items) - 1):
        swapped = False
        for j in range(len(items) - 1 - i):
            if comp(items[j], items[j+1]):
                items[j], items[j+1] = items[j+1], items[j]
                swapped = True
        if not swapped:
            break
    return items


# 搅拌排序,冒泡排序升级版
# 多排一次序，先正着排然后倒着排
def new_bubble_sort(items, comp=lambda x, y: x > y):
    items = items[:]
    for i in range(len(items) - 1):
        swapped = False
        for j in range(len(items) - 1 - i):
            if comp(items[j], items[j+1]):
                items[j], items[j+1] = items[j+1], items[j]
                swapped = True
        if swapped:
            swapped = False
            for j in range(len(items) - 2 - i, i, -1):
                if comp(items[j-1], items[j]):
                    items[j], items[j-1] = items[j-1], items[j]
                    swapped = True
        if not swapped:
            break
    return items


# 归并排序是利用归并的思想实现的排序方法
# 该算法采用了经典的分治策略（divide-and-conquer）
# 分治法将分（divide）成一些小的问题然后递归求解
# 而治（conquer）的阶段则将分的阶段得到的各个答案修补在一起

# 合并将两个有序的列表合并成一个有序的列表
def merge(items1, items2, comp=lambda x, y: x < y):
    items = []
    index1, index2 = 0, 0
    while index1 < len(items1) and index2 < len(items2):
        if comp(items1[index1], items2[index2]):
            items.append(items1[index1])
            index1 += 1
        else:
            items.append(items2[index2])
            index2 += 1
    items += items1[index1:]
    items += items2[index2:]
    return items


def merge_sort(items, comp=lambda x, y: x < y):
    return _merge_sort(list(items), comp)


# 合并递归处理
def _merge_sort(items, comp):
    if len(items) < 2:
        return items

    mid = len(items) // 2
    left = _merge_sort(items[:mid], comp)
    right = _merge_sort(items[mid:], comp)
    return merge(left, right, comp)


# 快速排序-选择枢轴对元素进行划分，左边都比枢轴小右边都比枢轴大
def quick_sort(items, comp=lambda x, y: x <= y):
    items = list(items)[:]
    _quick_sort(items, 0, len(items)-1, comp)
    return items


# 递归处理
def _quick_sort(items, start, end, comp):
    if start < end:
        pos = _partition(items, start, end, comp)
        _quick_sort(items, start, pos-1, comp)
        _quick_sort(items, pos+1, end, comp)


# 分而治之
def _partition(items, start, end, comp):
    pivot = items[end]
    i = start - 1
    for j in range(start, end):
        if comp(items[j], pivot):
            i += 1
            items[i], items[j] = items[j], items[i]
    items[i + 1], items[end] = items[end], items[i + 1]
    return i + 1


# 顺序查找
def seq_search(items, key):
    for index, item in enumerate(items):
        if item == key:
            return index
    return -1


# 折半查找
def bin_search(items, key):
    start, end = 0, len(items)-1
    while start <= end:
        mid = (start+end) // 2
        if key > items[mid]:
            start = mid + 1
        elif key < items[mid]:
            end = mid - 1
        else:
            return mid
    return -1


'''
    常用算法：
        穷举法：对所有的可能性继续验证，直到找到正确答案
        贪婪法：在对问题求解时，总是做出在当前看来最好的选择，不追求最优解，快速找到满意解
        分治法：把一个复杂的问题分成两个或更多的相同或相似的子问题
        再把子问题分成更小的子问题，直到可以直接求解的程度，最后将子问题
        的解进行合并得到原问题的解
        回溯法：回溯法又称为试探法，按选优条件向前搜索，当搜索到某一步发现原先
        选择并不优或达不到目标时，就退回一步重新选择
        动态规划：基本思想也是将待求解的问题分解成若干个子问题，先求解并保存这些子问题的解
        避免产生大量运算
'''
if __name__ == '__main__':
    items = [3, 7, 9, 2, 4]
    items1 = [1, 6, 8, 5, 10]
    a = select_sort(items)
    b = bubble_sort(items)
    c = merge(items, items1)
    d = merge_sort(c)
    f = seq_search(items, 7)
    g = bin_search(d, 8)
    h = quick_sort(items)
    print(d, g, h)
