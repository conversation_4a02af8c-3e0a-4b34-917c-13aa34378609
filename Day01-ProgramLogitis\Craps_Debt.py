from random import randint

# Craps赌博
money = 1
while money > 0:
    print("您的总资产为： ", money)
    needs_go_on = False
    while True:
        debt = int(input("请下注："))
        if 0 < debt <= money:
            break
    first = randint(1, 6) + randint(1, 6)
    print("玩家摇出了{}点".format(first))
    if first == 7 or first == 11:
        print("玩家胜！")
        money += debt
    elif first == 2 or first == 3 or first == 12:
        print("庄家胜！")
        money -= debt
    else:
        needs_go_on = True
    while needs_go_on:
        needs_go_on = False
        current = randint(1, 6)+randint(1, 6)
        print("玩家摇出了{}点".format(current))
        if current == 7:
            print("庄家胜！")
            money -= debt
        elif current == first:
            print("玩家胜！")
            money += debt
        else:
            needs_go_on = True
print("你破产了，游戏结束！")
