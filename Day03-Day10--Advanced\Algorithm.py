# Knight's Tour Problem using Backtracking method
# This approach tries different moves and backtracks when it realizes a chosen path does not lead to a solution.

SIZE = 8  # Chessboard size
total_solutions = 0  # Counter for total number of solutions found
def print_board(board):
    """Prints the chessboard in a formatted way."""
    for row in board:
        print(" ".join(str(col).center(4) for col in row))
    print("\n")

def is_valid_move(board, row, col):
    """Checks if a move is valid."""
    return 0 <= row < SIZE and 0 <= col < SIZE and board[row][col] == 0

def patrol(board, row, col, step=1):
    global total_solutions
    if is_valid_move(board, row, col):
        board[row][col] = step
        if step == SIZE * SIZE:
            total_solutions += 1
            print(f'Solution #{total_solutions}:')
            print_board(board)
        else:
            # Clockwise possible moves for a Knight
            moves = [(-2, -1), (-1, -2), (1, -2), (2, -1), (2, 1), (1, 2), (-1, 2), (-2, 1)]
            for dr, dc in moves:
                patrol(board, row + dr, col + dc, step + 1)
        board[row][col] = 0  # Backtrack

def main():
    board = [[0] * SIZE for _ in range(SIZE)]
    patrol(board, 0, 0)  # Starting from the top-left corner
if __name__ == '__main__':
    main()
