import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from sklearn.impute import SimpleImputer
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score

def convert_ratio(x, num_parts):
    """将以 ':' 分隔的字符串拆分为多个整数，确保有固定的部分数"""
    try:
        parts = str(x).split(':')
        if len(parts) != num_parts:
            # 如果部分数不足，补充NaN
            parts += [np.nan] * (num_parts - len(parts))
        return [int(part) if part.isdigit() else np.nan for part in parts[:num_parts]]
    except:
        return [np.nan] * num_parts

def preprocess_dataframe(df, special_columns_info):
    """
    预处理数据框：
    - 拆分特殊列
    - 编码分类特征
    - 创建滞后特征
    - 填充缺失值
    """
    data = df.copy()
    print(f"原始数据形状: {data.shape}")
    
    # 拆分所有特殊列
    for col, num_parts in special_columns_info.items():
        if col in data.columns:
            data[col] = data[col].apply(lambda x: convert_ratio(x, num_parts))
            for i in range(num_parts):
                data[f'{col}_{i+1}'] = data[col].apply(lambda x: x[i] if isinstance(x, list) and i < len(x) else np.nan)
            data = data.drop(col, axis=1)
    print(f"拆分特殊列后的数据形状: {data.shape}")
    
    # 初始化编码器字典
    encoders = {}
    
    # 对所有非数值型特征进行编码
    for col in data.columns:
        if data[col].dtype == 'object':
            try:
                data[col] = pd.to_numeric(data[col])
            except ValueError:
                le = LabelEncoder()
                data[col] = le.fit_transform(data[col].astype(str))
                encoders[col] = le
                
    # 创建滞后特征
    target_columns = list(special_columns_info.keys())
    for target in target_columns:
        for lag in range(1, 6):  # 例如，创建1到5期的滞后特征
            data[f'{target}_lag_{lag}'] = data[target].shift(lag)
    
    print(f"添加滞后特征后的数据形状: {data.shape}")
    
    # 填充缺失值
    imputer = SimpleImputer(strategy='mean')
    data_imputed = pd.DataFrame(imputer.fit_transform(data), columns=data.columns, index=data.index)
    
    print(f"填充NaN后的数据形状: {data_imputed.shape}")
    
    return data_imputed, encoders

def encode_targets(df, target_columns):
    """
    对目标列进行编码，如果需要的话
    返回各目标列的编码器字典
    """
    encoders = {}
    for target in target_columns:
        if df[target].dtype == 'object':
            le = LabelEncoder()
            df[target] = le.fit_transform(df[target].astype(str))
            encoders[target] = le
    return encoders

def train_and_evaluate_models(df, target_columns, top_n_predictions=3):
    """
    针对每个目标列训练模型并进行评估
    """
    results = {}
    for target in target_columns:
        print(f"\n正在训练和评估目标: {target}")
        
        # 定义特征X和标签y
        feature_cols = [col for col in df.columns if col != target and not col.startswith(f'{target}_lag_')]
        X = df[feature_cols].values
        y = df[target].values
        
        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 检查训练集是否足够
        if len(X_train) < 10:
            print(f"警告: 训练数据量太少 ({len(X_train)} 条记录)，无法训练模型。")
            results[target] = {'status': '训练数据不足'}
            continue
        
        # 训练分类模型
        model = RandomForestClassifier(n_estimators=100, random_state=42)
        model.fit(X_train, y_train)
        
        # 预测
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        print(f"模型准确率: {accuracy:.2f}")
        
        # 获取概率预测
        y_proba = model.predict_proba(X_test)
        top_predictions = []
        for proba, pred in zip(y_proba, y_pred):
            # 获取预测概率的前top_n
            top_indices = proba.argsort()[-top_n_predictions:][::-1]
            top_preds = [(model.classes_[i], proba[i]) for i in top_indices]
            top_predictions.append(top_preds)
        
        results[target] = {
            'accuracy': accuracy,
            'top_predictions': top_predictions,
            'y_test': y_test,
            'y_pred': y_pred
        }
    
    return results

def main(start_period, n_periods):
    # 读取Excel文件
    df = pd.read_excel('analysisball/ssq/analysis.xlsx')
    df.set_index('期号', inplace=True)
    df = df.iloc[::-1]  # 翻转数据，使最新的数据在上面
    
    periods = df.index.tolist()
    if start_period not in periods:
        print(f"起始期号 {start_period} 不存在于数据中。")
        return
    
    start_index = periods.index(start_period)
    
    print(f"总数据量: {len(df)}")
    print(f"开始索引: {start_index}")
    print(f"使用的周期数: {n_periods}")
    
    # 使用更多的历史数据
    data_to_use = df.iloc[:start_index + n_periods]
    print(f"当前使用的数据量: {len(data_to_use)}")
    
    # 定义需要拆分的特殊列及其部分数
    special_columns_info = {
        '红球奇偶比': 2,    # 例如 '5:1' 拆分为两个部分
        '红球区间比': 3,    # 例如 '4:2:0' 拆分为三个部分
        # 如果有其他需要拆分的列，可以在这里添加
    }
    
    # 预处理数据
    data_processed, encoders = preprocess_dataframe(data_to_use, special_columns_info)
    
    # 定义目标列
    target_columns = ['红球奇偶比_1', '红球奇偶比_2', '红球区间比_1', '红球区间比_2', '红球区间比_3', 
                      '红球连续数', '上期重复数']
    
    # 检查目标列是否存在
    for target in target_columns:
        if target not in data_processed.columns:
            print(f"目标列 {target} 不存在于数据中，跳过。")
            target_columns.remove(target)
    
    if not target_columns:
        print("没有有效的目标列进行预测。")
        return
    
    # 训练并评估模型
    results = train_and_evaluate_models(data_processed, target_columns, top_n_predictions=3)
    
    # 打印结果汇总
    print("\n汇总结果：")
    for target, result in results.items():
        if result.get('status') == '训练数据不足':
            print(f"{target}预测：训练数据不足，无法评估。")
        else:
            print(f"{target}预测：")
            print(f"  模型准确率: {result['accuracy']:.2f}")
            # 可以进一步处理 top_predictions 或其他评估指标
    
if __name__ == "__main__":
    main(2024001, 100)  # 您可以根据需要调整起始期号和使用的周期数
