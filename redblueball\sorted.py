from random import randint, sample
from collections import Counter
import re

# a = []
# with open('1.txt', 'r') as f:
#     for line in f.readlines():
#         line = line.strip('\n')
#         a.append(line)
        # print(line)
        # f.write(line)
    # print(a)
    # print(str(a))

# with open('1.txt', 'w') as f:
#     f.write(str(a))
# with open('1.txt') as f:
#     data = f.readlines()
#     print(type(data))
#     for i in range(len(data)):
#         data[i] = data[i].replace('\n', '')
#     data.sort()
#     # print(data)
#     # sys.stdout = Logger('2.txt')
#     for i in range(len(data)):
#         print(data[i])
# text = '020607242829|16'
text = '010414151732|02'
# print(type(text))
# print(str(text))

with open('rand/777.txt') as f:
    data = f.readlines()
    blueball = []
    # print(type(data[0]))
    # print(type(data[0]))
    # print(data[0])
    # print(data[0][:2])
    for i in range(len(data)):
        if text in data[i]:
            blueball.append(data[i].split('|')[1].replace('\n', ''))
            print("{}: {}".format(i, data[i]))
    print(Counter(blueball))
