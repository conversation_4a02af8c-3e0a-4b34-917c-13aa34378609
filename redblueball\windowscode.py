import itertools
import random
import numpy as np
from itertools import combinations
import sys


class Logger(object):
    def __init__(self, fileN='Default.log'):
        self.terminal = sys.stdout
        self.log = open(fileN, 'w')

    def write(self, message):
        '''print实际相当于sys.stdout.write'''
        self.terminal.write(message)
        self.log.write(message)
        self.log.flush()

    def flush(self):
        self.log.close()


def combine(temp_list, n):
    '''根据n获得列表中的所有可能组合（n个元素为一组）'''
    with open('./fivecharacter.txt', 'w') as f:
        for c in combinations(temp_list, n):
            f.writelines(str(c) + '\n')
    # temp_list2 = np.array([])
    # for c in combinations(temp_list, n):
    #     c = np.array(c)
    #     temp_list2 = np.append(temp_list2, c)
    # np.save('./fivecharacter.npy', temp_list2)


array = ['B', 'C', 'D', 'F', 'G', 'H', 'J', 'K', 'M', 'N', 'P', 'Q', 'R', 'T', 'V', 'W', 'X', 'Y', 'Z', 2, 3, 4, 6, 7, 8, 9]
# array = ['A',2,3,4,5,6]
ran1 = random.sample(array, 5)
ran2 = random.sample(array, 5)
ran3 = random.sample(array, 5)
ran4 = random.sample(array, 5)
ran5 = random.sample(array, 5)
ran = []
ran.extend(ran1)
ran.extend(ran2)
ran.extend(ran3)
ran.extend(ran4)
ran.extend(ran5)
print(len(ran))
print(ran)
for i in ran:
    print(i)
# combine(array, 5)
# c = np.load('./fivecharacter.npy')
# c = c.tolist()
# with open('fivecharacter.txt', 'r') as f:
    # sys.stdout = Logger('fivecode.txt')
    # a = f.readlines()
    # for i in range(len(a)):
    #     a[i] = a[i].replace('(', '').replace(', ', '').replace(')', '').replace('\n', '').replace('\'', '')
    #     print(a[i])
        # pailie = list(itertools.permutations(a[i]))  # 要list一下，不然它只是一个对象
        # for x in pailie:
        #     # print(type(x))
        #     for y in x:
        #         print(y, end='')
        #     print()
# print(total)
# a = (1, 2, 3, 4, 5)
# print(type(a))
# print(str(a))
# pailie = list(itertools.permutations(c))  # 要list一下，不然它只是一个对象
# for x in pailie:
#     for y in x:
#         print(y, end='')
#     print()
