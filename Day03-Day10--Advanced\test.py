import numpy as np


def is_valid_move(x, y, board_size, board):
    # 检查移动是否在棋盘内且未被访问过
    return 0 <= x < board_size and 0 <= y < board_size and board[x][y] == 0

def knight_tour(x, y, move_num, board, x_moves, y_moves, board_size, paths):
    # 如果已经完成了所有的移动
    if move_num == board_size**2:
        paths.append(board.copy())
        return

    for i in range(8):
        # 尝试所有可能的8个方向
        next_x, next_y = x + x_moves[i], y + y_moves[i]
        if is_valid_move(next_x, next_y, board_size, board):
            # 如果是有效的移动，标记该位置
            board[next_x][next_y] = move_num + 1
            # 递归继续进行下一步的移动
            knight_tour(next_x, next_y, move_num + 1, board, x_moves, y_moves, board_size, paths)
            # 回溯，恢复状态
            board[next_x][next_y] = 0
def knights_tours(board_size):
    # 初始化棋盘
    board = np.zeros((board_size, board_size), dtype=int)
    # 定义骑士的8个可能的移动方向
    x_moves = [2, 1, -1, -2, -2, -1, 1, 2]
    y_moves = [1, 2, 2, 1, -1, -2, -2, -1]
    paths = []
    # 从(0, 0)位置开始移动
    board[0][0] = 1
    knight_tour(0, 0, 1, board, x_moves, y_moves, board_size, paths)
    return paths

def main():
    board_size = 6  # 设定棋盘大小为8x8
    paths = knights_tours(board_size)

    # 输出所有有效路径的棋盘状态
    for i, path_board in enumerate(paths):
        print(f'路径 {i + 1}:')
        for row in path_board:
            print(' '.join(f'{cell:2}' for cell in row))
        print('-' * 20)

if __name__ == "__main__":
    main()
