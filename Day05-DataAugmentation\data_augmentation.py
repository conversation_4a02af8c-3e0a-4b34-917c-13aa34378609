# 数据增强：裁剪、旋转、反转、缩放
import random
import os
import numpy as np
import cv2 as cv
from PIL import Image, ImageEnhance
import matplotlib.pyplot as plt


img_path = r'img'
save_path = r'newimg'


def crop(img):
    h, w, _ = img.shape
    new_h1, new_h2 = np.random.randint(0, h - 256, 2)
    new_w1, new_w2 = np.random.randint(0, w - 256, 2)
    img_crop1 = img[new_h1:new_h1 + 256, new_w1:new_w1 + 256, :]
    img_crop2 = img[new_h2:new_h2 + 256, new_w2:new_w2 + 256, :]
    return img_crop1, img_crop2


# 去除黑边的操作
crop_image = lambda img, x0, y0, w, h: img[x0:x0+w, y0:y0+h]  # 定义裁切函数，后续裁切黑边使用


def rotate_image(img, angle, crop):
    """
    angle: 旋转的角度
    crop: 是否需要进行裁剪，布尔向量
    """
    w, h = img.shape[:2]
    # 旋转角度的周期是360°
    angle %= 360
    # 计算仿射变换矩阵
    # 第一个参数是旋转中心，第二个是逆时针旋转角度，第三个是缩放倍数，对于只是旋转的情况就为1
    M_rotation = cv.getRotationMatrix2D((w / 2, h / 2), angle, 1)
    # 得到旋转后的图像
    # warpAffine将原图像矩阵乘以旋转矩阵得到最终的结果
    # 可实现旋转、平移、缩放；变换后的平行线依旧平行
    img_rotated = cv.warpAffine(img, M_rotation, (w, h))

    # 如果需要去除黑边
    if crop:
        # 裁剪角度的等效周期是180°
        angle_crop = angle % 180
        if angle > 90:
            angle_crop = 180 - angle_crop
        # 转化角度为弧度
        theta = angle_crop * np.pi / 180
        # 计算高宽比
        hw_ratio = float(h) / float(w)
        # 计算裁剪边长系数的分子项
        tan_theta = abs(np.tan(theta))
        numerator = abs(np.cos(theta) + np.sin(theta) * np.tan(theta))

        # 计算分母中和高宽比相关的项
        r = hw_ratio if h > w else 1 / hw_ratio
        # 计算分母项
        denominator = r * tan_theta + 1
        # 最终的边长系数
        crop_mult = numerator / denominator

        # 得到裁剪区域
        w_crop = int(crop_mult * w)
        h_crop = int(crop_mult * h)
        x0 = int((w - w_crop) / 2)
        y0 = int((h - h_crop) / 2)
        #x0 = int(w_crop / 2)
        #y0 = int(h_crop / 2)
        img_rotated = crop_image(img_rotated, x0, y0, w_crop, h_crop)
    return img_rotated


def imgresize(img):
    img_2 = cv.resize(img, (int(h * 1.5), int(w * 1.5)))
    img_2 = img_2[int((h - 256) / 2): int((h + 256) / 2), int((w - 256) / 2): int((w + 256) / 2), :]
    img_3 = cv.resize(img, (256, 256))
    return img_2, img_3


def sp_noise(image,prob):
    '''
    添加椒盐噪声
    prob:噪声比例
    '''
    output = np.zeros(image.shape,np.uint8)
    thres = 1 - prob
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            rdn = random.random()
            if rdn < prob:
                output[i][j] = 0
            elif rdn > thres:
                output[i][j] = 255
            else:
                output[i][j] = image[i][j]
    return output


def gasuss_noise(image, mean=0, var=0.001):
    '''
        添加高斯噪声
        mean : 均值
        var : 方差
    '''
    image = np.array(image/255, dtype=float)
    noise = np.random.normal(mean, var ** 0.5, image.shape)
    out = image + noise
    if out.min() < 0:
        low_clip = -1.
    else:
        low_clip = 0.
    out = np.clip(out, low_clip, 1.0)
    out = np.uint8(out*255)
    #cv.imshow("gasuss", out)
    return out


def randomColor(image, saturation=0, brightness=0, contrast=0, sharpness=0):
    if random.random() < saturation:
        random_factor = np.random.randint(0, 31) / 10.  # 随机因子
        image = ImageEnhance.Color(image).enhance(random_factor)  # 调整图像的饱和度
    if random.random() < brightness:
        random_factor = np.random.randint(10, 21) / 10.  # 随机因子
        image = ImageEnhance.Brightness(image).enhance(random_factor)  # 调整图像的亮度
    if random.random() < contrast:
        random_factor = np.random.randint(10, 21) / 10.  # 随机因子
        image = ImageEnhance.Contrast(image).enhance(random_factor)  # 调整图像对比度
    if random.random() < sharpness:
        random_factor = np.random.randint(0, 31) / 10.  # 随机因子
        ImageEnhance.Sharpness(image).enhance(random_factor)  # 调整图像锐度
    return image


if __name__ == '__main__':
    files = os.listdir(img_path)
    # print('files', files)
    if not os.path.exists(save_path):
        os.mkdir(save_path)

    for fi in files:
        # print('fi: ', fi)
        filename = fi.split('.')[0]
        filedir = os.path.join(img_path, fi)
        # print('filedir', filedir)
        img = cv.imread(filedir)
        img = cv.cvtColor(img, cv.COLOR_BGR2RGB)
        h, w, _ = img.shape
        im = Image.open(filedir)
        out1 = im.transpose(Image.FLIP_LEFT_RIGHT)
        out2 = im.transpose(Image.FLIP_TOP_BOTTOM)
        out3 = im.transpose(Image.ROTATE_90)
        out4 = im.transpose(Image.ROTATE_180)
        out5 = im.transpose(Image.ROTATE_270)
        out1.save(save_path+'/'+'{}_{}.jpg'.format(filename, 1))
        out2.save(save_path+'/'+'{}_{}.jpg'.format(filename, 2))
        out3.save(save_path+'/'+'{}_{}.jpg'.format(filename, 3))
        out4.save(save_path+'/'+'{}_{}.jpg'.format(filename, 4))
        out5.save(save_path+'/'+'{}_{}.jpg'.format(filename, 5))
        img_rotate = rotate_image(img, 45, True)
        cv.imwrite('6.jpg', img_rotate)