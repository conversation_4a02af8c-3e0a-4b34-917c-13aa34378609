import pandas as pd
import numpy as np

def predict_ratio(df, ratio_column, n_periods):
    recent_data = df.head(n_periods)
    
    # 使用指数衰减的权重，给予近期数据更高的权重
    alpha = 0.1  # 衰减因子，可根据需要调整
    weights = np.exp(-alpha * np.arange(len(recent_data)))
    weights = pd.Series(weights[::-1], index=recent_data.index)  # 确保权重与数据对齐
    
    ratio_values = recent_data[ratio_column]
    weighted_counts = ratio_values.groupby(ratio_values).apply(lambda x: weights.loc[x.index].sum())
    total_weight = weights.sum()
    weighted_probs = weighted_counts / total_weight
    predictions = sorted(weighted_probs.items(), key=lambda x: x[1], reverse=True)
    
    # 返回概率最高的一个结果
    top_prediction = predictions[0] if predictions else None
    return top_prediction, "加权频率预测"

def calculate_last_digit(df):
    for i in range(1, 7):
        df[f'红球{i}尾数'] = df[f'红球{i}'] % 10
    df['红球尾数列表'] = df[[f'红球{i}尾数' for i in range(1, 7)]].values.tolist()
    return df

def predict_last_digit(df, n_periods, cumulative_threshold=0.6):
    recent_data = df.head(n_periods)
    last_digits = recent_data['红球尾数列表'].explode()
    counts = last_digits.value_counts()
    total = counts.sum()
    probs = (counts / total).sort_values(ascending=False)
    
    cumulative_probs = probs.cumsum()
    selected_digits = probs[cumulative_probs <= cumulative_threshold].index.tolist()
    
    # 如果累计概率未达到阈值，包含下一个尾数
    if len(selected_digits) == 0 or cumulative_probs.iloc[len(selected_digits)-1] < cumulative_threshold:
        if len(selected_digits) < len(probs):
            selected_digits.append(probs.index[len(selected_digits)])
    
    selected_probs = probs.loc[selected_digits]
    predictions = list(zip(selected_digits, selected_probs))
    return predictions, "尾数频率预测"

def predict_odd_even_ratio(df, n_periods):
    return predict_ratio(df, '红球奇偶比', n_periods)
        
def predict_interval_ratio(df, n_periods):
    return predict_ratio(df, '红球区间比', n_periods)
        
def predict_consecutive_numbers(df, n_periods):
    return predict_ratio(df, '红球连续数', n_periods)
        
def predict_repeated_numbers(df, n_periods):
    return predict_ratio(df, '上期重复数', n_periods)
        
def check_prediction(prediction, actual, prediction_type):
    if actual is not None:
        if prediction is None:
            return "无预测结果"
        if prediction_type == '红球尾数':
            pred_values = set(int(digit) for digit in prediction)
            actual_values = set(int(digit) for digit in actual)
            return "对" if pred_values.issubset(actual_values) else "错"
        else:
            pred_value = prediction[0]
            return "对" if pred_value == actual else "错"
    else:
        return "数据尚未公布"
        
def main(start_period, n_periods):
    df = pd.read_excel('analysisball/ssq/analysis.xlsx')
    df.set_index('期号', inplace=True)
    df = calculate_last_digit(df)
    
    periods = df.index.tolist()
    if start_period not in periods:
        raise ValueError(f"起始期号 {start_period} 不在数据中，请检查期号是否正确。")
    start_index = periods.index(start_period)
    
    stats = {
        '奇偶比': {'正确': 0, '错误': 0},
        '区间比': {'正确': 0, '错误': 0},
        '红球连续数': {'正确': 0, '错误': 0},
        '上期重复数': {'正确': 0, '错误': 0},
        '红球尾数': {'正确': 0, '错误': 0}
    }
    
    for i in range(start_index, 0, -1):
        current_period = periods[i]
        next_period = periods[i - 1] if i > 0 else None
        
        data_to_use = df.iloc[i:min(i + n_periods, len(df))]
        
        if len(data_to_use) < n_periods:
            continue

        # 奇偶比预测
        odd_even_prediction, odd_even_method = predict_odd_even_ratio(data_to_use, n_periods)
        # 区间比预测
        interval_prediction, interval_method = predict_interval_ratio(data_to_use, n_periods)
        # 红球连续数预测
        consecutive_prediction, consecutive_method = predict_consecutive_numbers(data_to_use, n_periods)
        # 上期重复数预测
        repeated_prediction, repeated_method = predict_repeated_numbers(data_to_use, n_periods)
        # 红球尾数预测
        last_digit_predictions, last_digit_method = predict_last_digit(data_to_use, n_periods, cumulative_threshold=0.6)
        predicted_digits = [int(pred[0]) for pred in last_digit_predictions]
        
        actual_data = {
            '奇偶比': df.loc[next_period, '红球奇偶比'] if next_period in df.index else None,
            '区间比': df.loc[next_period, '红球区间比'] if next_period in df.index else None,
            '红球连续数': df.loc[next_period, '红球连续数'] if next_period in df.index else None,
            '上期重复数': df.loc[next_period, '上期重复数'] if next_period in df.index else None,
            '红球尾数列表': df.loc[next_period, '红球尾数列表'] if next_period in df.index else None
        }
        
        print(f"预测 {next_period} 期结果：")
        
        # 打印并统计奇偶比预测结果
        result = check_prediction(odd_even_prediction, actual_data['奇偶比'], '奇偶比')
        print(f"奇偶比预测 (方法: {odd_even_method}): {odd_even_prediction} - {result}")
        update_stats(stats, '奇偶比', result)
        
        # 打印并统计区间比预测结果
        result = check_prediction(interval_prediction, actual_data['区间比'], '区间比')
        print(f"区间比预测 (方法: {interval_method}): {interval_prediction} - {result}")
        update_stats(stats, '区间比', result)
        
        # 打印并统计红球连续数预测结果
        result = check_prediction(consecutive_prediction, actual_data['红球连续数'], '红球连续数')
        print(f"红球连续数预测 (方法: {consecutive_method}): {consecutive_prediction} - {result}")
        update_stats(stats, '红球连续数', result)
        
        # 打印并统计上期重复数预测结果
        result = check_prediction(repeated_prediction, actual_data['上期重复数'], '上期重复数')
        print(f"上期重复数预测 (方法: {repeated_method}): {repeated_prediction} - {result}")
        update_stats(stats, '上期重复数', result)
        
        # 打印并统计红球尾数预测结果
        actual_last_digits = actual_data['红球尾数列表']
        if actual_last_digits is not None:
            result = check_prediction(predicted_digits, actual_last_digits, '红球尾数')
            print(f"红球尾数预测 (方法: {last_digit_method}): 预测尾数 {predicted_digits} - {result}")
            update_stats(stats, '红球尾数', result)
        else:
            print("红球尾数预测：数据尚未公布")
        
        print("\n")
        
    print_summary(stats)

def update_stats(stats, category, result):
    if result == "对":
        stats[category]['正确'] += 1
    elif result == "错":
        stats[category]['错误'] += 1

def print_summary(stats):
    print("汇总结果：")
    for category, stat in stats.items():
        total = stat['正确'] + stat['错误']
        accuracy = stat['正确'] / total if total > 0 else 0
        print(f"{category}预测：正确 {stat['正确']} 次，错误 {stat['错误']} 次，准确率 {accuracy:.2%}")

if __name__ == "__main__":
    main(2024001, 30)  # 请根据您的数据调整期号和分析期数
