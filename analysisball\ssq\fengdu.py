import pandas as pd
import numpy as np

# 读取Excel文件
df = pd.read_excel('analysisball/ssq/analysis-test.xlsx')

# 预测红球和值
def predict_red_sum(df, n_periods):
    recent_data = df.head(n_periods)
    diff_ma5 = recent_data['红球和值'] - recent_data['红球和值MA5']
    diff_ema5 = recent_data['红球和值'] - recent_data['红球和值EMA5']
    
    changes = recent_data['红球和值'].diff().abs()
    avg_change = changes.mean()
    std_change = changes.std()
    
    lower_bound = max(0, avg_change - 2 * std_change)
    upper_bound = avg_change + 2 * std_change
    
    if diff_ma5.iloc[0] > 0 and diff_ema5.iloc[0] > 0:
        prediction = "下降"
    elif diff_ma5.iloc[0] < 0 and diff_ema5.iloc[0] < 0:
        prediction = "上升"
    else:
        prediction = "不确定"
    
    return f"{prediction}，变化幅度在{lower_bound:.2f}到{upper_bound:.2f}之间"

def predict_ratio(df, ratio_column, n_periods):
    recent_data = df.head(n_periods)
    
    # 获取最近的几个比率值，长度最多为5
    ratios = recent_data[ratio_column].iloc[:min(5, len(recent_data))].tolist()
    
    current_sum = recent_data['红球和值'].iloc[0]
    
    # 构建历史模式匹配的条件
    conditions = [(recent_data[ratio_column].shift(i) == ratios[i]) for i in range(len(ratios))]
    historical_patterns = recent_data[np.logical_and.reduce(conditions)]
    
    if len(historical_patterns) > 0:
        next_ratios = historical_patterns[ratio_column].shift(-1).dropna()
        if len(next_ratios) > 0:
            value_counts = next_ratios.value_counts()
            top_3 = value_counts.nlargest(3)
            predictions = [(ratio, count / len(next_ratios)) for ratio, count in top_3.items()]
            return predictions, "历史模式", len(historical_patterns)
    
    counts = recent_data[ratio_column].value_counts()
    total = counts.sum()
    top_3 = counts.nlargest(3)
    predictions = [(ratio, count / total) for ratio, count in top_3.items()]
    
    return predictions, "频率预测", 0

# 预测奇偶比
def predict_odd_even_ratio(df, n_periods):
    return predict_ratio(df, '红球奇偶比', n_periods)

# 预测区间比
def predict_interval_ratio(df, n_periods):
    return predict_ratio(df, '红球区间比', n_periods)

# 预测红球连续数
def predict_consecutive_numbers(df, n_periods):
    return predict_ratio(df, '红球连续数', n_periods)

# 预测上期重复数
def predict_repeated_numbers(df, n_periods):
    return predict_ratio(df, '上期重复数', n_periods)

def check_prediction(prediction, actual):
    if actual is not None:
        return "对" if prediction == actual else "错"
    else:
        return None  # 返回 None 而不是 '数据尚未公布'

def main(start_period, n_periods):
    # 读取Excel文件，并设置期号为索引
    df = pd.read_excel('analysisball/ssq/analysis.xlsx')
    df.set_index('期号', inplace=True)
    
    # 初始化统计字典
    stats = {
        '红球和值预测': {'对': 0, '错': 0},
        '奇偶比预测': [{'对': 0, '错': 0}, {'对': 0, '错': 0}, {'对': 0, '错': 0}],
        '区间比预测': [{'对': 0, '错': 0}, {'对': 0, '错': 0}, {'对': 0, '错': 0}],
        '红球连续数预测': [{'对': 0, '错': 0}, {'对': 0, '错': 0}, {'对': 0, '错': 0}],
        '上期重复数预测': [{'对': 0, '错': 0}, {'对': 0, '错': 0}, {'对': 0, '错': 0}]
    }
    
    # 获取期号列表，并找到起始期号的位置
    periods = df.index.tolist()
    start_index = periods.index(start_period)
    
    # 从起始期号开始，向下迭代进行预测
    for i in range(start_index, -1, -1):
        current_period = periods[i]
        next_period = periods[i - 1] if i > 0 else None
        
        # 获取当前期号后 n_periods 的数据
        data_to_use = df.iloc[i:min(i + n_periods, len(df))]
        
        if len(data_to_use) < n_periods:
            continue  # 如果数据不足，则跳过
        
        # 进行预测
        red_sum_prediction = predict_red_sum(data_to_use, n_periods)
        odd_even_predictions, odd_even_method, _ = predict_odd_even_ratio(data_to_use, n_periods)
        interval_predictions, interval_method, _ = predict_interval_ratio(data_to_use, n_periods)
        consecutive_predictions, consecutive_method, _ = predict_consecutive_numbers(data_to_use, n_periods)
        repeated_predictions, repeated_method, _ = predict_repeated_numbers(data_to_use, n_periods)
        
        # 获取下一期的实际结果
        actual_data = {
            '红球和值': df.loc[next_period, '红球和值'] if next_period in df.index else None,
            '奇偶比': df.loc[next_period, '红球奇偶比'] if next_period in df.index else None,
            '区间比': df.loc[next_period, '红球区间比'] if next_period in df.index else None,
            '红球连续数': df.loc[next_period, '红球连续数'] if next_period in df.index else None,
            '上期重复数': df.loc[next_period, '上期重复数'] if next_period in df.index else None
        }
        
        # 更新统计字典
        red_sum_result = check_prediction(red_sum_prediction, actual_data['红球和值'])
        if red_sum_result:
            stats['红球和值预测'][red_sum_result] += 1
        
        for j, (pred, _) in enumerate(odd_even_predictions):
            result = check_prediction(pred, actual_data['奇偶比'])
            if result:
                stats['奇偶比预测'][j][result] += 1
        
        for j, (pred, _) in enumerate(interval_predictions):
            result = check_prediction(pred, actual_data['区间比'])
            if result:
                stats['区间比预测'][j][result] += 1
        
        for j, (pred, _) in enumerate(consecutive_predictions):
            result = check_prediction(pred, actual_data['红球连续数'])
            if result:
                stats['红球连续数预测'][j][result] += 1
        
        for j, (pred, _) in enumerate(repeated_predictions):
            result = check_prediction(pred, actual_data['上期重复数'])
            if result:
                stats['上期重复数预测'][j][result] += 1
        
        # 输出结果
        print(f"预测 {next_period} 期结果：")
        print(f"红球和值预测: {red_sum_prediction} - {red_sum_result or '数据尚未公布'}")
        print(f"奇偶比预测 (方法: {odd_even_method}):")
        for pred, prob in odd_even_predictions:
            print(f"  {pred}，概率: {prob:.2f} - {check_prediction(pred, actual_data['奇偶比']) or '数据尚未公布'}")
        print(f"区间比预测 (方法: {interval_method}):")
        for pred, prob in interval_predictions:
            print(f"  {pred}，概率: {prob:.2f} - {check_prediction(pred, actual_data['区间比']) or '数据尚未公布'}")
        print(f"红球连续数预测 (方法: {consecutive_method}):")
        for pred, prob in consecutive_predictions:
            print(f"  {pred}，概率: {prob:.2f} - {check_prediction(pred, actual_data['红球连续数']) or '数据尚未公布'}")
        print(f"上期重复数预测 (方法: {repeated_method}):")
        for pred, prob in repeated_predictions:
            print(f"  {pred}，概率: {prob:.2f} - {check_prediction(pred, actual_data['上期重复数']) or '数据尚未公布'}")
        print("\n")
    
    # 输出汇总结果
    print("汇总结果：")
    print(f"红球和值预测: 错{stats['红球和值预测']['错']}次 对{stats['红球和值预测']['对']}次")
    
    print("奇偶比预测:")
    for j, counts in enumerate(stats['奇偶比预测']):
        print(f"  错{counts['错']}次 对{counts['对']}次")
    
    print("区间比预测:")
    for j, counts in enumerate(stats['区间比预测']):
        print(f"  错{counts['错']}次 对{counts['对']}次")
    
    print("红球连续数预测:")
    for j, counts in enumerate(stats['红球连续数预测']):
        print(f"  错{counts['错']}次 对{counts['对']}次")
    
    print("上期重复数预测:")
    for j, counts in enumerate(stats['上期重复数预测']):
        print(f"  错{counts['错']}次 对{counts['对']}次")

if __name__ == "__main__":
    main(2024093, 20)

