import random
import sys


class Logger(object):
    def __init__(self, fileN='Default.log'):
        self.terminal = sys.stdout
        self.log = open(fileN, 'w')

    def write(self, message):
        '''print实际相当于sys.stdout.write'''
        self.terminal.write(message)
        self.log.write(message)
        self.log.flush()

    def flush(self):
        self.log.close()


if __name__ == '__main__':
    array = ['B', 'C', 'D', 'F', 'G', 'H', 'J', 'K', 'M', 'N', 'P', 'Q', 'R', 'T', 'V', 'W', 'X', 'Y', 'Z', 2, 3, 4, 6, 7, 8, 9]
    # array = [2,3,4,5,6]
    # ran25 = random.sample(array, 25)
    sys.stdout = Logger('25-code.txt')
    for j in range(100):
        ran1 = random.sample(array, 5)
        ran2 = random.sample(array, 5)
        ran3 = random.sample(array, 5)
        ran4 = random.sample(array, 5)
        ran5 = random.sample(array, 5)
        ran = []
        ran.extend(ran1)
        ran.extend(ran2)
        ran.extend(ran3)
        ran.extend(ran4)
        ran.extend(ran5)
        count = 0
        # for i in range(5, len(ran25)-1, 5):
        #     a = i + count
        #     ran25.insert(a, '-')
        #     count = count + 1
        # ran25 = ('').join(str(i) for i in ran25)
        for i in range(5, len(ran)-1, 5):
            a = i + count
            ran.insert(a, '-')
            count = count + 1
        ran = ('').join(str(i) for i in ran)
        # print(ran25)
        print(ran)
