# Python安全升级脚本
# 作者：Augment Agent
# 用途：安全地将conda base环境的Python从3.10升级到3.12

Write-Host "=== Python安全升级脚本 ===" -ForegroundColor Green
Write-Host "当前时间: $(Get-Date)" -ForegroundColor Yellow

# 步骤1：检查当前状态
Write-Host "`n步骤1：检查当前环境状态..." -ForegroundColor Cyan
Write-Host "当前Python版本："
python --version
Write-Host "当前conda版本："
conda --version

# 步骤2：备份环境信息
Write-Host "`n步骤2：备份环境信息..." -ForegroundColor Cyan
$backupDir = "python_upgrade_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
New-Item -ItemType Directory -Path $backupDir -Force

Write-Host "创建备份目录: $backupDir"

# 导出base环境包列表
Write-Host "备份base环境包列表..."
conda list --name base --export > "$backupDir\base_environment.txt"

# 导出所有环境信息
Write-Host "备份所有环境信息..."
conda info --envs > "$backupDir\conda_environments.txt"

# 导出conda配置
Write-Host "备份conda配置..."
conda config --show > "$backupDir\conda_config.txt"

Write-Host "备份完成！备份文件保存在: $backupDir" -ForegroundColor Green

# 步骤3：检查可用的Python版本
Write-Host "`n步骤3：检查可用的Python版本..." -ForegroundColor Cyan
Write-Host "可用的Python版本："
conda search python | Select-String "python.*3\.1[2-9]" | Select-Object -First 10

# 步骤4：询问用户确认
Write-Host "`n步骤4：确认升级..." -ForegroundColor Cyan
$confirmation = Read-Host "是否继续升级Python到3.12？(y/N)"

if ($confirmation -eq 'y' -or $confirmation -eq 'Y') {
    Write-Host "`n开始升级Python..." -ForegroundColor Yellow
    
    # 步骤5：执行升级
    Write-Host "正在更新Python到3.12..."
    conda update python=3.12 -y
    
    # 步骤6：验证升级结果
    Write-Host "`n步骤6：验证升级结果..." -ForegroundColor Cyan
    Write-Host "新的Python版本："
    python --version
    
    Write-Host "Python包信息："
    conda list python
    
    # 步骤7：测试基本功能
    Write-Host "`n步骤7：测试基本功能..." -ForegroundColor Cyan
    python -c "import sys; print(f'Python {sys.version}'); print('基本功能正常')"
    
    Write-Host "`n=== 升级完成！ ===" -ForegroundColor Green
    Write-Host "备份文件位置: $backupDir" -ForegroundColor Yellow
    Write-Host "如果遇到问题，可以使用备份文件恢复环境" -ForegroundColor Yellow
    
} else {
    Write-Host "升级已取消" -ForegroundColor Red
}

# 步骤8：显示虚拟环境状态
Write-Host "`n步骤8：检查虚拟环境状态..." -ForegroundColor Cyan
Write-Host "所有虚拟环境："
conda info --envs

Write-Host "`n脚本执行完成！" -ForegroundColor Green
