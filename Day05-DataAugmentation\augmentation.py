import cv2 as cv
import numpy as np
from PIL import Image, ImageEnhance
import random
import matplotlib.pyplot as plt


img_path = '../tic-tac-toe/tic tac opening.png'
img = cv.imread(img_path)
img = cv.cvtColor(img, cv.COLOR_BGR2RGB)

# Crop
# 做裁剪操作主要是考虑原始图像的宽高扰动，在大多数图像分类网络中，样本在输入网络前
# 必须要统一大小，所以通过调整图像的尺寸可以大量的扩展数据
h, w, _ = img.shape
new_h1, new_h2 = np.random.randint(0, h-256, 2)
new_w1, new_w2 = np.random.randint(0, w-256, 2)
img_crop1 = img[new_h1:new_h1+256, new_w1:new_w1+256, :]
img_crop2 = img[new_h2:new_h2+256, new_w2:new_w2+256, :]

plt.figure(figsize=(15, 10))
plt.subplot(1,3,1), plt.imshow(img)
plt.axis('off'); plt.title('img')
plt.subplot(1,3,2), plt.imshow(img_crop1)
plt.axis('off'); plt.title('img1')
plt.subplot(1,3,3), plt.imshow(img_crop2)
plt.axis('off'); plt.title('img2')
plt.show()

# Flip and Rotate
# 翻转和旋转都是将原始的图像像素在位置空间上做变换，图像的反转是将原始的图像进行镜像操作
# 镜像操作在数据增强中会经常被使用，并且起了非常重要的作用，它主要包括水平镜像翻转
# 有水平镜像翻转，垂直镜像翻转和原点镜像翻转，具体在使用中，需要结合数据形式选择相应翻转操作
# 角度旋转操作和图像镜像相对，他主要是沿着画面的中心进行任意角度的变换，该变换是通过将源图像和仿射变换矩阵相乘实现的
# 为了实现图像的中心旋转，除了要知道旋转角度，还要计算平移的量才能让仿射变换的效果等效于旋转轴的画面中心

# 去除黑边的操作
crop_image = lambda img, x0, y0, w, h: img[y0:y0+h, x0:x0+w]  # 定义裁切函数，后续裁切黑边使用

def rotate_image(img, angle, crop):
    """
    angle: 旋转的角度
    crop: 是否需要进行裁剪，布尔向量
    """
    w, h = img.shape[:2]
    # 旋转角度的周期是360°
    angle %= 360
    # 计算仿射变换矩阵
    # 第一个参数是旋转中心，第二个是逆时针旋转角度，第三个是缩放倍数，对于只是旋转的情况就为1
    M_rotation = cv.getRotationMatrix2D((w / 2, h / 2), angle, 1)
    # 得到旋转后的图像
    # warpAffine将原图像矩阵乘以旋转矩阵得到最终的结果
    # 可实现旋转、平移、缩放；变换后的平行线依旧平行
    img_rotated = cv.warpAffine(img, M_rotation, (w, h))

    # 如果需要去除黑边
    if crop:
        # 裁剪角度的等效周期是180°
        angle_crop = angle % 180
        if angle > 90:
            angle_crop = 180 - angle_crop
        # 转化角度为弧度
        theta = angle_crop * np.pi / 180
        # 计算高宽比
        hw_ratio = float(h) / float(w)
        # 计算裁剪边长系数的分子项
        tan_theta = np.tan(theta)
        numerator = np.cos(theta) + np.sin(theta) * np.tan(theta)

        # 计算分母中和高宽比相关的项
        r = hw_ratio if h > w else 1 / hw_ratio
        # 计算分母项
        denominator = r * tan_theta + 1
        # 最终的边长系数
        crop_mult = numerator / denominator

        # 得到裁剪区域
        w_crop = int(crop_mult * w)
        h_crop = int(crop_mult * h)
        x0 = int((w - w_crop) / 2)
        y0 = int((h - h_crop) / 2)
        #x0 = int(w_crop / 2)
        #y0 = int(h_crop / 2)
        img_rotated = crop_image(img_rotated, x0, y0, w_crop, h_crop)
    return img_rotated
#水平镜像
h_flip = cv.flip(img,1)
#垂直镜像
v_flip = cv.flip(img,0)
#水平垂直镜像
hv_flip = cv.flip(img,-1)
#90度旋转
rows, cols, _ = img.shape
M = cv.getRotationMatrix2D((cols/2, rows/2), 45, 1)
rotation_45 = cv.warpAffine(img, M, (cols, rows))
#45度旋转
M = cv.getRotationMatrix2D((cols/2, rows/2), 135, 2)
rotation_135 = cv.warpAffine(img, M,(cols, rows))
#去黑边旋转45度
image_rotated = rotate_image(img, 45, True)

#显示
plt.figure(figsize=(15, 10))
plt.subplot(2,3,1), plt.imshow(img)
plt.axis('off'); plt.title('img')
plt.subplot(2,3,2), plt.imshow(h_flip)
plt.axis('off'); plt.title('h_flip')
plt.subplot(2,3,3), plt.imshow(v_flip)
plt.axis('off'); plt.title('v_flip')
plt.subplot(2,3,4), plt.imshow(hv_flip)
plt.axis('off'); plt.title('hv_flip')
plt.subplot(2,3,5), plt.imshow(rotation_45)
plt.axis('off'); plt.title('rotation_45')
plt.subplot(2,3,6), plt.imshow(image_rotated)
plt.axis('off'); plt.title('image_rotated')
plt.show()

# 缩放
'''
图像可以向外或向内缩放。向外缩放时，最终图像尺寸将大于原始图像尺寸，为了保持原始图像的大小
通常需要结合裁剪，从缩放后的图像中裁剪出和原始图像大小一样的图像。
另一种方法是向内缩放，它会缩小图像大小，缩小到预设的大小。缩放也会带来一些问题
如缩放后的图像尺寸和原始图像尺寸的长度比差异较大，会出现图像失帧的现象
如果在实验中对最终的结果有一定的影响，需要做等比例缩放，对不足的地方进行边缘填充
'''

img_2 = cv.resize(img, (int(h * 1.5), int(w * 1.5)))
img_2 = img_2[int((h - 256) / 2): int((h + 256) / 2), int((w - 256) / 2): int((w + 256) /2), :]
img_3 = cv.resize(img, (256, 256))

# 显示
plt.figure(figsize=(15, 10))
plt.subplot(1,3,1), plt.imshow(img)
plt.axis('off'); plt.title('origin_img')
plt.subplot(1,3,2), plt.imshow(img_2)
plt.axis('off'); plt.title('outscale')
plt.subplot(1,3,3), plt.imshow(img_3)
plt.axis('off'); plt.title('innerscale')
plt.show()

# Shift
'''
移位只涉及沿X或Y方向（或两者）移动图像，如果图像的背景是单色背景或者是纯的
黑色背景，使用该方法可以很有效的增强数据数量，可以通过warpAffine实现
'''

# mat_shift = np.float32([[1,0,100], [0,1,200]])
# img_1 = cv.warpAffine(img, mat_shift, (h, w))
# mat_shift = np.float32([[1, 0, -150], [0, 1, -150]])
# img_2 = cv.warpAffine(img, mat_shift, (h, w))
#
# # 显示
# plt.figure(figsize=(15, 10))
# plt.subplot(1,3,1), plt.imshow(img)
# plt.axis('off'); plt.title('origin img')
# plt.subplot(1,3,2), plt.imshow(img_1)
# plt.axis('off'); plt.title('under right')
# plt.subplot(1,3,3), plt.imshow(img_2)
# plt.axis('off'); plt.title('up left')
# plt.show()

# Gaussian
'''
当神经网络试图学习可能无用的高频特征时，通常会发生过度拟合
具有零均值的高斯噪声基本在所有频率中具有数据点，从而有效地扭曲高频特征。
这也意味着较低频率的组件也会失真，但你的神经网络可以超越它，添加适量的噪音可以增强学习能力
基于噪声的数据增强就是在原来的图片的基础上，随机叠加一些杂声，最常见的做法就是高斯噪声
更复杂一点的就是在面积大小可选定、位置随机的矩形区域上丢弃像素产生黑色矩形块，从而产生一些彩色噪声
以Coarse Dropout方法为代表，甚至还可以对图片上随机选取一块区域并擦除图像信息
'''


def sp_noise(image,prob):
    '''
    添加椒盐噪声
    prob:噪声比例
    '''
    output = np.zeros(image.shape,np.uint8)
    thres = 1 - prob
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            rdn = random.random()
            if rdn < prob:
                output[i][j] = 0
            elif rdn > thres:
                output[i][j] = 255
            else:
                output[i][j] = image[i][j]
    return output
def gasuss_noise(image, mean=0, var=0.001):
    '''
        添加高斯噪声
        mean : 均值
        var : 方差
    '''
    image = np.array(image/255, dtype=float)
    noise = np.random.normal(mean, var ** 0.5, image.shape)
    out = image + noise
    if out.min() < 0:
        low_clip = -1.
    else:
        low_clip = 0.
    out = np.clip(out, low_clip, 1.0)
    out = np.uint8(out*255)
    #cv.imshow("gasuss", out)
    return out


img_s1 = gasuss_noise(img, 0, 0.005)
img_s2 = gasuss_noise(img, 0, 0.05)
plt.figure(figsize=(15, 10))
plt.subplot(1,3,1), plt.imshow(img)
plt.axis('off'); plt.title('origin img')
plt.subplot(1,3,2), plt.imshow(img_s1)
plt.axis('off'); plt.title('img_0.005')
plt.subplot(1,3,3), plt.imshow(img_s2)
plt.axis('off'); plt.title('img_0.05')
plt.show()

'''
色彩抖动
上面提到的图像中有一个比较大的难点是背景干扰，是实际工程中为了消除图像在不同背景中存在的差异性，通常会做一些色彩抖动操作，扩充数据集合
色彩抖动主要是在图像的颜色方面做增强，主要调整的是图像的亮度，饱和度和对比度。
工程中不是任何数据集都适用，通常如果不同背景的图像较多，加入色彩抖动操作会有很好的提升
'''

def randomColor(image, saturation=0, brightness=0, contrast=0, sharpness=0):
    if random.random() < saturation:
        random_factor = np.random.randint(0, 31) / 10.  # 随机因子
        image = ImageEnhance.Color(image).enhance(random_factor)  # 调整图像的饱和度
    if random.random() < brightness:
        random_factor = np.random.randint(10, 21) / 10.  # 随机因子
        image = ImageEnhance.Brightness(image).enhance(random_factor)  # 调整图像的亮度
    if random.random() < contrast:
        random_factor = np.random.randint(10, 21) / 10.  # 随机因子
        image = ImageEnhance.Contrast(image).enhance(random_factor)  # 调整图像对比度
    if random.random() < sharpness:
        random_factor = np.random.randint(0, 31) / 10.  # 随机因子
        ImageEnhance.Sharpness(image).enhance(random_factor)  # 调整图像锐度
    return image


cj_img = Image.fromarray(img)
sa_img = np.asarray(randomColor(cj_img, saturation=1))
br_img = np.asarray(randomColor(cj_img, brightness=1))
co_img = np.asarray(randomColor(cj_img, contrast=1))
sh_img = np.asarray(randomColor(cj_img, sharpness=1))
rc_img = np.asarray(randomColor(cj_img, saturation=1, brightness=1, contrast=1, sharpness=1))
plt.figure(figsize=(15, 10))
plt.subplot(2,3,1), plt.imshow(img)
plt.axis('off'); plt.title('origin_img')
plt.subplot(2,3,2), plt.imshow(sa_img)
plt.axis('off'); plt.title('saturation')
plt.subplot(2,3,3), plt.imshow(br_img)
plt.axis('off'); plt.title('brightness')
plt.subplot(2,3,4), plt.imshow(co_img)
plt.axis('off'); plt.title('contrast')
plt.subplot(2,3,5), plt.imshow(sh_img)
plt.axis('off'); plt.title('sharpness')
plt.subplot(2,3,6), plt.imshow(rc_img)
plt.axis('off'); plt.title('change all')
plt.show()
